import 'menu_item.dart';

class CartItem {
  final String id;
  final MenuItem menuItem;
  final String restaurantId;
  int quantity;
  final List<String> specialInstructions;

  CartItem({
    required this.id,
    required this.menuItem,
    required this.restaurantId,
    this.quantity = 1,
    this.specialInstructions = const [],
  });

  double get totalPrice => menuItem.price * quantity;

  CartItem copyWith({
    String? id,
    MenuItem? menuItem,
    String? restaurantId,
    int? quantity,
    List<String>? specialInstructions,
  }) =>
      CartItem(
        id: id ?? this.id,
        menuItem: menuItem ?? this.menuItem,
        restaurantId: restaurantId ?? this.restaurantId,
        quantity: quantity ?? this.quantity,
        specialInstructions: specialInstructions ?? this.specialInstructions,
      );

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
        id: json['id'],
        menuItem: MenuItem.fromJson(json['menuItem']),
        restaurantId: json['restaurantId'],
        quantity: json['quantity'],
        specialInstructions: json['specialInstructions'] != null ? List<String>.from(json['specialInstructions']) : [],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'menuItem': menuItem.toJson(),
        'restaurantId': restaurantId,
        'quantity': quantity,
        'specialInstructions': specialInstructions,
      };
}
