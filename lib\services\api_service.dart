import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/restaurant.dart';
import '../models/menu_item.dart';
import '../models/order.dart';
import '../core/app_constants.dart';
import 'error_service.dart';
import 'audit_service.dart';

/// Comprehensive API service for all backend communication
class ApiService extends GetxService {
  static ApiService get instance => Get.find<ApiService>();

  // HTTP client
  late final http.Client _client;
  
  // Base configuration
  late final String _baseUrl;
  final Map<String, String> _defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // Authentication
  final RxString _authToken = ''.obs;
  final RxBool _isAuthenticated = false.obs;
  
  // Request tracking
  final RxInt _activeRequests = 0.obs;
  final RxBool _isOnline = true.obs;
  
  // Services
  late final ErrorService _errorService;
  late final AuditService _auditService;
  
  // Configuration
  static const Duration requestTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Getters
  String get authToken => _authToken.value;
  bool get isAuthenticated => _isAuthenticated.value;
  int get activeRequests => _activeRequests.value;
  bool get isOnline => _isOnline.value;
  String get baseUrl => _baseUrl;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeApi();
  }

  @override
  void onClose() {
    _client.close();
    super.onClose();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
    _auditService = Get.find<AuditService>();
  }

  void _initializeApi() {
    // Set base URL based on environment
    _baseUrl = EnvironmentConfig.apiBaseUrl;
    
    // Initialize HTTP client
    _client = http.Client();
    
    // Load saved auth token
    _loadAuthToken();
    
    // Set up connectivity monitoring
    _setupConnectivityMonitoring();
  }

  Future<void> _loadAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConstants.userTokenKey);
      if (token != null && token.isNotEmpty) {
        _authToken.value = token;
        _isAuthenticated.value = true;
        _updateAuthHeaders();
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'ApiService.loadAuthToken');
    }
  }

  Future<void> _saveAuthToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userTokenKey, token);
      _authToken.value = token;
      _isAuthenticated.value = true;
      _updateAuthHeaders();
    } catch (e) {
      _errorService.handleAppError(e, context: 'ApiService.saveAuthToken');
    }
  }

  Future<void> clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userTokenKey);
      _authToken.value = '';
      _isAuthenticated.value = false;
      _defaultHeaders.remove('Authorization');
    } catch (e) {
      _errorService.handleAppError(e, context: 'ApiService.clearAuthToken');
    }
  }

  void _updateAuthHeaders() {
    if (_authToken.value.isNotEmpty) {
      _defaultHeaders['Authorization'] = 'Bearer ${_authToken.value}';
    }
  }

  void _setupConnectivityMonitoring() {
    // Simple connectivity check - in production, use connectivity_plus package
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkConnectivity();
    });
  }

  Future<void> _checkConnectivity() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/health'),
        headers: _defaultHeaders,
      ).timeout(const Duration(seconds: 5));
      
      _isOnline.value = response.statusCode == 200;
    } catch (e) {
      _isOnline.value = false;
    }
  }

  // Generic HTTP methods
  Future<ApiResponse<T>> _makeRequest<T>(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    bool requiresAuth = true,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    if (requiresAuth && !_isAuthenticated.value) {
      return ApiResponse.error('Authentication required');
    }

    _activeRequests.value++;
    
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final requestHeaders = {..._defaultHeaders, ...?headers};
      
      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(uri, headers: requestHeaders).timeout(requestTimeout);
          break;
        case 'POST':
          response = await _client.post(
            uri,
            headers: requestHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(requestTimeout);
          break;
        case 'PUT':
          response = await _client.put(
            uri,
            headers: requestHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await _client.delete(uri, headers: requestHeaders).timeout(requestTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }
      
      return _handleResponse<T>(response, fromJson);
      
    } on TimeoutException {
      return ApiResponse.error('Request timeout');
    } on SocketException {
      _isOnline.value = false;
      return ApiResponse.error('No internet connection');
    } catch (e) {
      _errorService.handleAppError(e, context: 'ApiService.makeRequest');
      return ApiResponse.error('Network error: ${e.toString()}');
    } finally {
      _activeRequests.value--;
    }
  }

  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      final data = jsonDecode(response.body) as Map<String, dynamic>;
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (fromJson != null && data['data'] != null) {
          return ApiResponse.success(fromJson(data['data']));
        } else {
          return ApiResponse.success(data as T);
        }
      } else {
        final message = data['message'] ?? 'Request failed';
        
        // Handle authentication errors
        if (response.statusCode == 401) {
          clearAuthToken();
          return ApiResponse.error('Authentication failed', statusCode: 401);
        }
        
        return ApiResponse.error(message, statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error('Failed to parse response');
    }
  }

  // Authentication endpoints
  Future<ApiResponse<UserModel>> login(String email, String password) async {
    final response = await _makeRequest<UserModel>(
      'POST',
      AppConstants.authEndpoint + '/login',
      body: {'email': email, 'password': password},
      requiresAuth: false,
      fromJson: (json) => UserModel.fromJson(json['user']),
    );
    
    if (response.isSuccess && response.data != null) {
      final token = (response.data as dynamic)['token'];
      if (token != null) {
        await _saveAuthToken(token);
      }
    }
    
    return response;
  }

  Future<ApiResponse<UserModel>> register(Map<String, dynamic> userData) async {
    return _makeRequest<UserModel>(
      'POST',
      AppConstants.authEndpoint + '/register',
      body: userData,
      requiresAuth: false,
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  Future<ApiResponse<void>> logout() async {
    final response = await _makeRequest<void>(
      'POST',
      AppConstants.authEndpoint + '/logout',
    );
    
    await clearAuthToken();
    return response;
  }

  // User endpoints
  Future<ApiResponse<UserModel>> getProfile() async {
    return _makeRequest<UserModel>(
      'GET',
      AppConstants.usersEndpoint + '/profile',
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  Future<ApiResponse<UserModel>> updateProfile(Map<String, dynamic> userData) async {
    return _makeRequest<UserModel>(
      'PUT',
      AppConstants.usersEndpoint + '/profile',
      body: userData,
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  // Restaurant endpoints
  Future<ApiResponse<List<RestaurantModel>>> getRestaurants({
    double? latitude,
    double? longitude,
    String? category,
    String? search,
  }) async {
    final queryParams = <String, String>{};
    if (latitude != null) queryParams['lat'] = latitude.toString();
    if (longitude != null) queryParams['lng'] = longitude.toString();
    if (category != null) queryParams['category'] = category;
    if (search != null) queryParams['search'] = search;
    
    final query = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    final endpoint = AppConstants.restaurantsEndpoint + (query.isNotEmpty ? '?$query' : '');
    
    return _makeRequest<List<RestaurantModel>>(
      'GET',
      endpoint,
      requiresAuth: false,
      fromJson: (json) => (json['restaurants'] as List)
          .map((item) => RestaurantModel.fromJson(item))
          .toList(),
    );
  }

  Future<ApiResponse<RestaurantModel>> getRestaurant(String restaurantId) async {
    return _makeRequest<RestaurantModel>(
      'GET',
      '${AppConstants.restaurantsEndpoint}/$restaurantId',
      requiresAuth: false,
      fromJson: (json) => RestaurantModel.fromJson(json),
    );
  }

  // Menu endpoints
  Future<ApiResponse<List<MenuItemModel>>> getMenu(String restaurantId) async {
    return _makeRequest<List<MenuItemModel>>(
      'GET',
      '${AppConstants.restaurantsEndpoint}/$restaurantId${AppConstants.menuEndpoint}',
      requiresAuth: false,
      fromJson: (json) => (json['items'] as List)
          .map((item) => MenuItemModel.fromJson(item))
          .toList(),
    );
  }

  // Order endpoints
  Future<ApiResponse<OrderModel>> createOrder(Map<String, dynamic> orderData) async {
    return _makeRequest<OrderModel>(
      'POST',
      AppConstants.ordersEndpoint,
      body: orderData,
      fromJson: (json) => OrderModel.fromJson(json),
    );
  }

  Future<ApiResponse<List<OrderModel>>> getOrders({
    OrderStatus? status,
    int? limit,
    int? offset,
  }) async {
    final queryParams = <String, String>{};
    if (status != null) queryParams['status'] = status.name;
    if (limit != null) queryParams['limit'] = limit.toString();
    if (offset != null) queryParams['offset'] = offset.toString();
    
    final query = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    final endpoint = AppConstants.ordersEndpoint + (query.isNotEmpty ? '?$query' : '');
    
    return _makeRequest<List<OrderModel>>(
      'GET',
      endpoint,
      fromJson: (json) => (json['orders'] as List)
          .map((item) => OrderModel.fromJson(item))
          .toList(),
    );
  }

  Future<ApiResponse<OrderModel>> getOrder(String orderId) async {
    return _makeRequest<OrderModel>(
      'GET',
      '${AppConstants.ordersEndpoint}/$orderId',
      fromJson: (json) => OrderModel.fromJson(json),
    );
  }

  Future<ApiResponse<OrderModel>> updateOrderStatus(String orderId, OrderStatus status) async {
    return _makeRequest<OrderModel>(
      'PUT',
      '${AppConstants.ordersEndpoint}/$orderId/status',
      body: {'status': status.name},
      fromJson: (json) => OrderModel.fromJson(json),
    );
  }

  // Payment endpoints
  Future<ApiResponse<Map<String, dynamic>>> processPayment(Map<String, dynamic> paymentData) async {
    return _makeRequest<Map<String, dynamic>>(
      'POST',
      AppConstants.paymentsEndpoint + '/process',
      body: paymentData,
      fromJson: (json) => json,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> refundPayment(String paymentId, Map<String, dynamic> refundData) async {
    return _makeRequest<Map<String, dynamic>>(
      'POST',
      '${AppConstants.paymentsEndpoint}/$paymentId/refund',
      body: refundData,
      fromJson: (json) => json,
    );
  }

  // File upload
  Future<ApiResponse<String>> uploadFile(String filePath, String endpoint) async {
    try {
      _activeRequests.value++;
      
      final request = http.MultipartRequest('POST', Uri.parse('$_baseUrl$endpoint'));
      request.headers.addAll(_defaultHeaders);
      
      final file = await http.MultipartFile.fromPath('file', filePath);
      request.files.add(file);
      
      final streamedResponse = await request.send().timeout(requestTimeout);
      final response = await http.Response.fromStream(streamedResponse);
      
      final data = jsonDecode(response.body) as Map<String, dynamic>;
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.success(data['url'] as String);
      } else {
        return ApiResponse.error(data['message'] ?? 'Upload failed');
      }
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'ApiService.uploadFile');
      return ApiResponse.error('Upload failed: ${e.toString()}');
    } finally {
      _activeRequests.value--;
    }
  }
}

/// API Response wrapper class
class ApiResponse<T> {
  final T? data;
  final String? error;
  final int? statusCode;
  final bool isSuccess;

  ApiResponse._({
    this.data,
    this.error,
    this.statusCode,
    required this.isSuccess,
  });

  factory ApiResponse.success(T data) {
    return ApiResponse._(
      data: data,
      isSuccess: true,
    );
  }

  factory ApiResponse.error(String error, {int? statusCode}) {
    return ApiResponse._(
      error: error,
      statusCode: statusCode,
      isSuccess: false,
    );
  }
}
