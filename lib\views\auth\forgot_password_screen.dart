import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../core/validators.dart';
import '../shared/custom_input_field.dart';
import '../shared/custom_buttons.dart';
import '../shared/snackbars.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final UserController _userController = Get.find<UserController>();

  bool _isCodeSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    return Validators.validateEmail(value);
  }

  String? _validateCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Verification code is required';
    }
    if (value.length != 6) {
      return 'Please enter a valid 6-digit code';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    return Validators.validatePassword(value, requireComplexity: true);
  }

  String? _validateConfirmPassword(String? value) {
    return Validators.validateConfirmPassword(value, _passwordController.text);
  }

  Future<void> _handleForgotPassword() async {
    if (_formKey.currentState!.validate()) {
      final success = await _userController.forgotPassword(
        _emailController.text.trim(),
      );

      if (success) {
        setState(() {
          _isCodeSent = true;
        });
        SuccessSnackBar.show('Password reset code sent to your email!');
      } else {
        ErrorSnackBar.show(_userController.errorMessage);
      }
    }
  }

  Future<void> _verifyCodeAndResetPassword() async {
    if (_formKey.currentState!.validate()) {
      final success = await _userController.resetPassword(
        _emailController.text.trim(),
        _codeController.text.trim(),
        _passwordController.text,
      );

      if (success) {
        SuccessSnackBar.show('Password reset successfully! Please login with your new password.');
        Get.back(); // Return to login screen
      } else {
        ErrorSnackBar.show(_userController.errorMessage);
      }
    }
  }

  void _resendCode() async {
    final success = await _userController.forgotPassword(_emailController.text.trim());

    if (success) {
      SuccessSnackBar.show('New verification code sent to your email');
    } else {
      ErrorSnackBar.show(_userController.errorMessage);
    }
  }

  void _navigateToLogin() {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
        title: const Text('Forgot Password'),
      ),
      body: Obx(() => _userController.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 40),

                    // Header
                    Center(
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              _isCodeSent ? Icons.mark_email_read : Icons.lock_reset,
                              size: 40,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            _isCodeSent ? 'Enter Verification Code' : 'Reset Password',
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _isCodeSent
                                ? 'We\'ve sent a 6-digit verification code to ${_emailController.text}. Enter it below along with your new password.'
                                : 'Enter your email address and we\'ll send you a verification code to reset your password.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Form
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Email Field (always visible but disabled after code is sent)
                          CustomInputField(
                            label: 'Email Address',
                            hint: 'Enter your email address',
                            controller: _emailController,
                            validator: _validateEmail,
                            keyboardType: TextInputType.emailAddress,
                            prefixIcon: const Icon(Icons.email_outlined),
                            required: true,
                            enabled: !_isCodeSent,
                          ),

                          if (_isCodeSent) ...[
                            const SizedBox(height: 16),

                            // Verification code field
                            CustomInputField(
                              label: 'Verification Code',
                              hint: 'Enter 6-digit code',
                              controller: _codeController,
                              validator: _validateCode,
                              keyboardType: TextInputType.number,
                              prefixIcon: const Icon(Icons.security),
                              required: true,
                              maxLength: 6,
                            ),
                            const SizedBox(height: 16),

                            // New password field
                            CustomInputField(
                              label: 'New Password',
                              hint: 'Enter your new password',
                              controller: _passwordController,
                              validator: _validatePassword,
                              prefixIcon: const Icon(Icons.lock),
                              obscureText: true,
                              required: true,
                            ),
                            const SizedBox(height: 16),

                            // Confirm password field
                            CustomInputField(
                              label: 'Confirm New Password',
                              hint: 'Confirm your new password',
                              controller: _confirmPasswordController,
                              validator: _validateConfirmPassword,
                              prefixIcon: const Icon(Icons.lock_outline),
                              obscureText: true,
                              required: true,
                            ),
                          ],

                          const SizedBox(height: 32),

                          // Action Button
                          Obx(() => PrimaryButton(
                                text: _isCodeSent ? 'Reset Password' : 'Send Reset Code',
                                onPressed: _isCodeSent ? _verifyCodeAndResetPassword : _handleForgotPassword,
                                isLoading: _userController.isLoading,
                                width: double.infinity,
                              )),

                          if (_isCodeSent) ...[
                            const SizedBox(height: 16),

                            // Resend code button
                            TextButton(
                              onPressed: _resendCode,
                              child: const Text('Didn\'t receive the code? Resend'),
                            ),
                            const SizedBox(height: 8),

                            // Change email button
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _isCodeSent = false;
                                  _codeController.clear();
                                  _passwordController.clear();
                                  _confirmPasswordController.clear();
                                });
                              },
                              child: const Text('Change Email Address'),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Back to Login
                    Center(
                      child: TextButton.icon(
                        onPressed: _navigateToLogin,
                        icon: const Icon(Icons.arrow_back),
                        label: const Text('Back to Login'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Help Text
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.blue,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Need Help?',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '• Check your spam/junk folder\n'
                            '• Make sure you entered the correct email\n'
                            '• Contact support if you continue having issues',
                            style: TextStyle(
                              color: Colors.grey[700],
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )),
    );
  }
}
