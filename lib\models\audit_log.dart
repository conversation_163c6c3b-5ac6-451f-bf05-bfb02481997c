/// Audit log model for tracking admin actions and system events
class AuditLog {
  final String id;
  final String userId;
  final String userName;
  final AuditAction action;
  final String entityType;
  final String entityId;
  final Map<String, dynamic>? oldValues;
  final Map<String, dynamic>? newValues;
  final String? reason;
  final String? ipAddress;
  final String? userAgent;
  final DateTime timestamp;
  final AuditSeverity severity;

  const AuditLog({
    required this.id,
    required this.userId,
    required this.userName,
    required this.action,
    required this.entityType,
    required this.entityId,
    this.oldValues,
    this.newValues,
    this.reason,
    this.ipAddress,
    this.userAgent,
    required this.timestamp,
    this.severity = AuditSeverity.info,
  });

  factory AuditLog.fromJson(Map<String, dynamic> json) => AuditLog(
        id: json['id'],
        userId: json['userId'],
        userName: json['userName'],
        action: AuditAction.values.firstWhere(
          (a) => a.name == json['action'],
          orElse: () => AuditAction.unknown,
        ),
        entityType: json['entityType'],
        entityId: json['entityId'],
        oldValues: json['oldValues'],
        newValues: json['newValues'],
        reason: json['reason'],
        ipAddress: json['ipAddress'],
        userAgent: json['userAgent'],
        timestamp: DateTime.parse(json['timestamp']),
        severity: AuditSeverity.values.firstWhere(
          (s) => s.name == json['severity'],
          orElse: () => AuditSeverity.info,
        ),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'userName': userName,
        'action': action.name,
        'entityType': entityType,
        'entityId': entityId,
        'oldValues': oldValues,
        'newValues': newValues,
        'reason': reason,
        'ipAddress': ipAddress,
        'userAgent': userAgent,
        'timestamp': timestamp.toIso8601String(),
        'severity': severity.name,
      };

  AuditLog copyWith({
    String? id,
    String? userId,
    String? userName,
    AuditAction? action,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    String? reason,
    String? ipAddress,
    String? userAgent,
    DateTime? timestamp,
    AuditSeverity? severity,
  }) =>
      AuditLog(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        userName: userName ?? this.userName,
        action: action ?? this.action,
        entityType: entityType ?? this.entityType,
        entityId: entityId ?? this.entityId,
        oldValues: oldValues ?? this.oldValues,
        newValues: newValues ?? this.newValues,
        reason: reason ?? this.reason,
        ipAddress: ipAddress ?? this.ipAddress,
        userAgent: userAgent ?? this.userAgent,
        timestamp: timestamp ?? this.timestamp,
        severity: severity ?? this.severity,
      );

  String get actionDescription {
    switch (action) {
      case AuditAction.create:
        return 'Created $entityType';
      case AuditAction.update:
        return 'Updated $entityType';
      case AuditAction.delete:
        return 'Deleted $entityType';
      case AuditAction.login:
        return 'User logged in';
      case AuditAction.logout:
        return 'User logged out';
      case AuditAction.passwordChange:
        return 'Password changed';
      case AuditAction.roleChange:
        return 'User role changed';
      case AuditAction.suspend:
        return 'Account suspended';
      case AuditAction.activate:
        return 'Account activated';
      case AuditAction.approve:
        return 'Entity approved';
      case AuditAction.reject:
        return 'Entity rejected';
      case AuditAction.refund:
        return 'Order refunded';
      case AuditAction.export:
        return 'Data exported';
      case AuditAction.import:
        return 'Data imported';
      case AuditAction.systemConfig:
        return 'System configuration changed';
      case AuditAction.loginFailed:
        return 'Login attempt failed';
      case AuditAction.accountLocked:
        return 'Account locked due to failed attempts';
      case AuditAction.emailVerified:
        return 'Email address verified';
      case AuditAction.passwordResetRequested:
        return 'Password reset requested';
      case AuditAction.passwordReset:
        return 'Password reset completed';
      case AuditAction.register:
        return 'User registered';
      case AuditAction.unknown:
        return 'Unknown action';
    }
  }

  bool get isHighRisk {
    return [
          AuditAction.delete,
          AuditAction.suspend,
          AuditAction.roleChange,
          AuditAction.systemConfig,
          AuditAction.refund,
        ].contains(action) ||
        severity == AuditSeverity.critical;
  }
}

/// Types of actions that can be audited
enum AuditAction {
  create,
  update,
  delete,
  login,
  logout,
  passwordChange,
  roleChange,
  suspend,
  activate,
  approve,
  reject,
  refund,
  export,
  import,
  systemConfig,
  loginFailed,
  accountLocked,
  emailVerified,
  passwordResetRequested,
  passwordReset,
  register,
  unknown,
}

/// Severity levels for audit events
enum AuditSeverity {
  info,
  warning,
  error,
  critical,
}

/// Extension methods for AuditAction
extension AuditActionExtension on AuditAction {
  String get displayName {
    switch (this) {
      case AuditAction.create:
        return 'Create';
      case AuditAction.update:
        return 'Update';
      case AuditAction.delete:
        return 'Delete';
      case AuditAction.login:
        return 'Login';
      case AuditAction.logout:
        return 'Logout';
      case AuditAction.passwordChange:
        return 'Password Change';
      case AuditAction.roleChange:
        return 'Role Change';
      case AuditAction.suspend:
        return 'Suspend';
      case AuditAction.activate:
        return 'Activate';
      case AuditAction.approve:
        return 'Approve';
      case AuditAction.reject:
        return 'Reject';
      case AuditAction.refund:
        return 'Refund';
      case AuditAction.export:
        return 'Export';
      case AuditAction.import:
        return 'Import';
      case AuditAction.systemConfig:
        return 'System Config';
      case AuditAction.loginFailed:
        return 'Login Failed';
      case AuditAction.accountLocked:
        return 'Account Locked';
      case AuditAction.emailVerified:
        return 'Email Verified';
      case AuditAction.passwordResetRequested:
        return 'Password Reset Requested';
      case AuditAction.passwordReset:
        return 'Password Reset';
      case AuditAction.register:
        return 'Register';
      case AuditAction.unknown:
        return 'Unknown';
    }
  }

  bool get requiresReason {
    return [
      AuditAction.delete,
      AuditAction.suspend,
      AuditAction.reject,
      AuditAction.refund,
    ].contains(this);
  }
}

/// Extension methods for AuditSeverity
extension AuditSeverityExtension on AuditSeverity {
  String get displayName {
    switch (this) {
      case AuditSeverity.info:
        return 'Info';
      case AuditSeverity.warning:
        return 'Warning';
      case AuditSeverity.error:
        return 'Error';
      case AuditSeverity.critical:
        return 'Critical';
    }
  }

  int get priority {
    switch (this) {
      case AuditSeverity.info:
        return 1;
      case AuditSeverity.warning:
        return 2;
      case AuditSeverity.error:
        return 3;
      case AuditSeverity.critical:
        return 4;
    }
  }
}
