# Restaurant Hub 🍽️

A comprehensive restaurant management and food delivery platform built with Flutter and GetX. This production-ready application features multi-role authentication, real-time order tracking, push notifications, location services, and a complete administration system.

## 📱 Features

### Core Features
- **Multi-Role Authentication**: Customer, Admin, Restaurant, and Driver roles with secure JWT authentication
- **Restaurant Discovery**: Browse restaurants with advanced filtering, search, and location-based recommendations
- **Menu Management**: Dynamic menu display with categories, customization options, and real-time availability
- **Order Management**: Complete order lifecycle from cart to delivery with status tracking
- **Real-Time Tracking**: Live order and delivery tracking with WebSocket integration
- **Push Notifications**: Firebase Cloud Messaging for order updates, promotions, and system alerts
- **Location Services**: GPS tracking, geolocation, and location-based restaurant discovery
- **Payment Integration**: Secure payment processing with multiple payment methods
- **Admin Dashboard**: Comprehensive administration panel for system management and analytics

### Production-Ready Features
- **Caching System**: Multi-level caching with Hive for optimal performance and offline support
- **Error Handling**: Comprehensive logging and crash reporting with Firebase Crashlytics
- **Performance Monitoring**: Real-time performance metrics, frame rate monitoring, and optimization
- **Security**: Input validation, secure data handling, and encrypted local storage
- **Scalability**: Modular architecture with dependency injection and service layer abstraction

## 📋 Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code with Flutter extensions
- Android SDK / Xcode (for iOS development)
- Firebase project setup
- Google Maps API key

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/restaurant-hub.git
   cd restaurant-hub
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Firebase**
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Enable Authentication, Firestore, Cloud Messaging, and Crashlytics

4. **Set up environment variables**
   ```bash
   # Create .env file in project root
   API_BASE_URL=https://your-api-url.com
   GOOGLE_MAPS_API_KEY=your_google_maps_key
   ```

5. **Run the application**
   ```bash
   flutter run
   ```

## 🏗️ Architecture & Project Structure

### Design Patterns
- **MVC/MVVM**: Clear separation of concerns with GetX controllers
- **Repository Pattern**: Data abstraction layer for API and local storage
- **Service Layer**: Dedicated services for business logic and external integrations
- **Dependency Injection**: GetX-based dependency management with lazy initialization

### Project Structure
```
lib/
├── controllers/          # GetX controllers for state management
│   ├── auth/            # Authentication controllers
│   ├── admin/           # Admin panel controllers
│   ├── customer/        # Customer-specific controllers
│   └── bindings/        # Dependency injection bindings
├── models/              # Data models and entities
├── services/            # Business logic and external services
│   ├── api_service.dart         # API integration and HTTP client
│   ├── authentication_service.dart # User authentication
│   ├── cache_service.dart       # Multi-level caching system
│   ├── location_service.dart    # GPS and location services
│   ├── notification_service.dart # Push notifications
│   ├── realtime_service.dart    # WebSocket connections
│   └── logging_service.dart     # Logging and crash reporting
├── views/               # UI screens and widgets
│   ├── auth/           # Authentication screens
│   ├── admin/          # Admin dashboard
│   ├── customer/       # Customer interface
│   └── shared/         # Reusable widgets
├── core/               # Core utilities and constants
├── routes/             # Navigation and routing
└── main.dart           # Application entry point
```

## 🧩 Adding Routes

To add new routes to the application, update the `lib/routes/app_routes.dart` file:

```dart
import 'package:flutter/material.dart';
import 'package:package_name/presentation/home_screen/home_screen.dart';

class AppRoutes {
  static const String initial = '/';
  static const String home = '/home';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    home: (context) => const HomeScreen(),
    // Add more routes as needed
  }
}
```

## 🎨 Theming

This project includes a comprehensive theming system with both light and dark themes:

```dart
// Access the current theme
ThemeData theme = Theme.of(context);

// Use theme colors
Color primaryColor = theme.colorScheme.primary;
```

The theme configuration includes:
- Color schemes for light and dark modes
- Typography styles
- Button themes
- Input decoration themes
- Card and dialog themes

## 📱 Responsive Design

The app is built with responsive design using the Sizer package:

```dart
// Example of responsive sizing
Container(
  width: 50, // 50% of screen width
  height: 20, // 20% of screen height
  child: Text('Responsive Container'),
)
```
## 📦 Deployment

Build the application for production:

```bash
# For Android
flutter build apk --release

# For iOS
flutter build ios --release
```

## 🙏 Acknowledgments
- Built with [Rocket.new](https://rocket.new)
- Powered by [Flutter](https://flutter.dev) & [Dart](https://dart.dev)
- Styled with Material Design