import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../core/app_constants.dart';

/// Error types for categorizing different kinds of errors
enum ErrorType {
  network,
  api,
  authentication,
  validation,
  business,
  framework,
  platform,
  application,
}

/// Error severity levels
enum ErrorSeverity {
  info,
  warning,
  error,
  critical,
}

/// Application error model
class AppError {
  final ErrorType type;
  final String message;
  final String stackTrace;
  final DateTime timestamp;
  final ErrorSeverity severity;
  final String? context;
  final int? statusCode;

  const AppError({
    required this.type,
    required this.message,
    required this.stackTrace,
    required this.timestamp,
    required this.severity,
    this.context,
    this.statusCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'message': message,
      'stackTrace': stackTrace,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity.name,
      'context': context,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'AppError(type: $type, severity: $severity, message: $message)';
  }
}

/// Comprehensive error handling service for the restaurant hub application
class ErrorService extends GetxService {
  static ErrorService get instance => Get.find<ErrorService>();

  final List<AppError> _errorHistory = [];
  final RxBool _isOnline = true.obs;

  List<AppError> get errorHistory => List.unmodifiable(_errorHistory);
  bool get isOnline => _isOnline.value;

  @override
  void onInit() {
    super.onInit();
    _initializeErrorHandling();
  }

  void _initializeErrorHandling() {
    // Set up global error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      handleFlutterError(details);
    };

    // Handle platform dispatcher errors
    PlatformDispatcher.instance.onError = (error, stack) {
      handlePlatformError(error, stack);
      return true;
    };
  }

  /// Handle Flutter framework errors
  void handleFlutterError(FlutterErrorDetails details) {
    final error = AppError(
      type: ErrorType.framework,
      message: details.exception.toString(),
      stackTrace: details.stack.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.error,
      context: details.context?.toString(),
    );

    _logError(error);
    _recordError(error);

    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  /// Handle platform-level errors
  void handlePlatformError(Object error, StackTrace stackTrace) {
    final appError = AppError(
      type: ErrorType.platform,
      message: error.toString(),
      stackTrace: stackTrace.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.critical,
    );

    _logError(appError);
    _recordError(appError);
  }

  /// Handle network-related errors
  void handleNetworkError(dynamic error, {String? context}) {
    final appError = AppError(
      type: ErrorType.network,
      message: _getNetworkErrorMessage(error),
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.warning,
      context: context,
    );

    _logError(appError);
    _recordError(appError);
    _updateNetworkStatus(false);

    // Show user-friendly message
    _showUserFriendlyError(appError);
  }

  /// Handle API-related errors
  void handleApiError(dynamic error, {String? endpoint, int? statusCode}) {
    final appError = AppError(
      type: ErrorType.api,
      message: _getApiErrorMessage(error, statusCode),
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: _getApiErrorSeverity(statusCode),
      context: endpoint,
      statusCode: statusCode,
    );

    _logError(appError);
    _recordError(appError);
    _showUserFriendlyError(appError);
  }

  /// Handle authentication errors
  void handleAuthError(dynamic error, {String? context}) {
    final appError = AppError(
      type: ErrorType.authentication,
      message: error.toString(),
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.error,
      context: context,
    );

    _logError(appError);
    _recordError(appError);

    // Redirect to login if needed
    if (appError.message.toLowerCase().contains('unauthorized') || appError.message.toLowerCase().contains('token')) {
      Get.offAllNamed('/login');
    }

    _showUserFriendlyError(appError);
  }

  /// Handle validation errors
  void handleValidationError(String message, {String? field}) {
    final appError = AppError(
      type: ErrorType.validation,
      message: message,
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.info,
      context: field,
    );

    _logError(appError);
    _recordError(appError);
    _showUserFriendlyError(appError);
  }

  /// Handle business logic errors
  void handleBusinessError(String message, {String? context}) {
    final appError = AppError(
      type: ErrorType.business,
      message: message,
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: ErrorSeverity.warning,
      context: context,
    );

    _logError(appError);
    _recordError(appError);
    _showUserFriendlyError(appError);
  }

  /// Handle general application errors
  void handleAppError(dynamic error, {String? context, ErrorSeverity? severity}) {
    final appError = AppError(
      type: ErrorType.application,
      message: error.toString(),
      stackTrace: StackTrace.current.toString(),
      timestamp: DateTime.now(),
      severity: severity ?? ErrorSeverity.error,
      context: context,
    );

    _logError(appError);
    _recordError(appError);
    _showUserFriendlyError(appError);
  }

  /// Log error to console and external services
  void _logError(AppError error) {
    // Console logging
    developer.log(
      error.message,
      name: 'RestaurantHub.${error.type.name}',
      error: error.message,
      stackTrace: StackTrace.fromString(error.stackTrace),
      level: _getLogLevel(error.severity),
    );

    // TODO: Send to external logging service (Firebase Crashlytics, Sentry, etc.)
    if (AppConstants.enableCrashReporting && !kDebugMode) {
      _sendToExternalService(error);
    }
  }

  /// Record error in local history
  void _recordError(AppError error) {
    _errorHistory.add(error);

    // Keep only recent errors to prevent memory issues
    if (_errorHistory.length > 100) {
      _errorHistory.removeAt(0);
    }
  }

  /// Show user-friendly error message
  void _showUserFriendlyError(AppError error) {
    String userMessage;

    switch (error.type) {
      case ErrorType.network:
        userMessage = AppConstants.networkErrorMessage;
        break;
      case ErrorType.api:
        userMessage = _getApiUserMessage(error.statusCode);
        break;
      case ErrorType.authentication:
        userMessage = AppConstants.authenticationErrorMessage;
        break;
      case ErrorType.validation:
        userMessage = error.message; // Validation messages are already user-friendly
        break;
      case ErrorType.business:
        userMessage = error.message; // Business messages are already user-friendly
        break;
      default:
        userMessage = AppConstants.serverErrorMessage;
    }

    // Only show snackbar for user-facing errors
    if (error.severity != ErrorSeverity.info && Get.context != null) {
      Get.snackbar(
        'Error',
        userMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: _getErrorColor(error.severity),
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
      );
    }
  }

  /// Get network error message
  String _getNetworkErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout')) {
      return 'Request timeout. Please check your connection.';
    } else if (errorString.contains('connection')) {
      return 'Connection failed. Please check your internet.';
    } else if (errorString.contains('host')) {
      return 'Server unreachable. Please try again later.';
    }

    return 'Network error occurred.';
  }

  /// Get API error message
  String _getApiErrorMessage(dynamic error, int? statusCode) {
    if (statusCode != null) {
      switch (statusCode) {
        case 400:
          return 'Bad request. Please check your input.';
        case 401:
          return 'Unauthorized. Please log in again.';
        case 403:
          return 'Access forbidden. You don\'t have permission.';
        case 404:
          return 'Resource not found.';
        case 429:
          return 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        case 503:
          return 'Service unavailable. Please try again later.';
        default:
          return 'API error occurred (Status: $statusCode).';
      }
    }

    return error.toString();
  }

  /// Get API error severity based on status code
  ErrorSeverity _getApiErrorSeverity(int? statusCode) {
    if (statusCode == null) return ErrorSeverity.error;

    if (statusCode >= 500) return ErrorSeverity.critical;
    if (statusCode >= 400) return ErrorSeverity.error;
    if (statusCode >= 300) return ErrorSeverity.warning;

    return ErrorSeverity.info;
  }

  /// Get user message for API errors
  String _getApiUserMessage(int? statusCode) {
    if (statusCode == null) return AppConstants.serverErrorMessage;

    if (statusCode == 401) return AppConstants.authenticationErrorMessage;
    if (statusCode == 403) return AppConstants.permissionErrorMessage;
    if (statusCode >= 500) return AppConstants.serverErrorMessage;
    if (statusCode >= 400) return AppConstants.validationErrorMessage;

    return AppConstants.serverErrorMessage;
  }

  /// Get log level for severity
  int _getLogLevel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return 800; // INFO
      case ErrorSeverity.warning:
        return 900; // WARNING
      case ErrorSeverity.error:
        return 1000; // SEVERE
      case ErrorSeverity.critical:
        return 1200; // SHOUT
    }
  }

  /// Get error color for UI
  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Colors.blue;
      case ErrorSeverity.warning:
        return Colors.orange;
      case ErrorSeverity.error:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red[900]!;
    }
  }

  /// Update network status
  void _updateNetworkStatus(bool isOnline) {
    _isOnline.value = isOnline;
  }

  /// Send error to external service
  void _sendToExternalService(AppError error) {
    // TODO: Implement external error reporting
    // Examples: Firebase Crashlytics, Sentry, Bugsnag
  }

  /// Clear error history
  void clearErrorHistory() {
    _errorHistory.clear();
  }

  /// Get errors by type
  List<AppError> getErrorsByType(ErrorType type) {
    return _errorHistory.where((error) => error.type == type).toList();
  }

  /// Get errors by severity
  List<AppError> getErrorsBySeverity(ErrorSeverity severity) {
    return _errorHistory.where((error) => error.severity == severity).toList();
  }
}
