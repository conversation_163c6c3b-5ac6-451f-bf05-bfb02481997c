import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../../core/app_routes.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final UserController userController = Get.find<UserController>();
    
    return Obx(() {
      final user = userController.currentUser;
      if (user == null) return const SizedBox.shrink();

      final items = _getNavigationItems(user.role);
      
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: items,
      );
    });
  }

  List<BottomNavigationBarItem> _getNavigationItems(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return [
          const BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.restaurant),
            label: 'Restaurants',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Cart',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.receipt),
            label: 'Orders',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      
      case UserRole.restaurant:
        return [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.restaurant_menu),
            label: 'Menu',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'Orders',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      
      case UserRole.driver:
        return [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.delivery_dining),
            label: 'Deliveries',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.attach_money),
            label: 'Earnings',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      
      case UserRole.admin:
        return [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Users',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ];
    }
  }
}

class MainNavigationController extends GetxController {
  final UserController _userController = Get.find<UserController>();
  
  final RxInt _currentIndex = 0.obs;
  int get currentIndex => _currentIndex.value;

  void changeTab(int index) {
    _currentIndex.value = index;
    _navigateToPage(index);
  }

  void _navigateToPage(int index) {
    final user = _userController.currentUser;
    if (user == null) return;

    String route;
    
    switch (user.role) {
      case UserRole.customer:
        route = _getCustomerRoute(index);
        break;
      case UserRole.restaurant:
        route = _getRestaurantRoute(index);
        break;
      case UserRole.driver:
        route = _getDriverRoute(index);
        break;
      case UserRole.admin:
        route = _getAdminRoute(index);
        break;
    }

    Get.offAllNamed(route);
  }

  String _getCustomerRoute(int index) {
    switch (index) {
      case 0:
        return AppRoutes.home;
      case 1:
        return AppRoutes.restaurants;
      case 2:
        return AppRoutes.cart;
      case 3:
        return AppRoutes.orders;
      case 4:
        return AppRoutes.profile;
      default:
        return AppRoutes.home;
    }
  }

  String _getRestaurantRoute(int index) {
    switch (index) {
      case 0:
        return AppRoutes.restaurantDashboard;
      case 1:
        return AppRoutes.menuManagement;
      case 2:
        return AppRoutes.orderManagement;
      case 3:
        return AppRoutes.analytics; // Restaurant analytics
      case 4:
        return AppRoutes.restaurantProfile;
      default:
        return AppRoutes.restaurantDashboard;
    }
  }

  String _getDriverRoute(int index) {
    switch (index) {
      case 0:
        return AppRoutes.driverDashboard;
      case 1:
        return AppRoutes.driverDashboard; // Available deliveries (same as dashboard for now)
      case 2:
        return AppRoutes.driverEarnings;
      case 3:
        return AppRoutes.driverProfile;
      default:
        return AppRoutes.driverDashboard;
    }
  }

  String _getAdminRoute(int index) {
    switch (index) {
      case 0:
        return AppRoutes.adminDashboard;
      case 1:
        return AppRoutes.userManagement;
      case 2:
        return AppRoutes.analytics;
      case 3:
        return AppRoutes.profile; // Admin settings/profile
      default:
        return AppRoutes.adminDashboard;
    }
  }

  int getIndexForRoute(String route) {
    final user = _userController.currentUser;
    if (user == null) return 0;

    switch (user.role) {
      case UserRole.customer:
        return _getCustomerIndex(route);
      case UserRole.restaurant:
        return _getRestaurantIndex(route);
      case UserRole.driver:
        return _getDriverIndex(route);
      case UserRole.admin:
        return _getAdminIndex(route);
    }
  }

  int _getCustomerIndex(String route) {
    switch (route) {
      case AppRoutes.home:
        return 0;
      case AppRoutes.restaurants:
        return 1;
      case AppRoutes.cart:
        return 2;
      case AppRoutes.orders:
        return 3;
      case AppRoutes.profile:
        return 4;
      default:
        return 0;
    }
  }

  int _getRestaurantIndex(String route) {
    switch (route) {
      case AppRoutes.restaurantDashboard:
        return 0;
      case AppRoutes.menuManagement:
        return 1;
      case AppRoutes.orderManagement:
        return 2;
      case AppRoutes.analytics:
        return 3;
      case AppRoutes.restaurantProfile:
        return 4;
      default:
        return 0;
    }
  }

  int _getDriverIndex(String route) {
    switch (route) {
      case AppRoutes.driverDashboard:
        return 0;
      case AppRoutes.driverEarnings:
        return 2;
      case AppRoutes.driverProfile:
        return 3;
      default:
        return 0;
    }
  }

  int _getAdminIndex(String route) {
    switch (route) {
      case AppRoutes.adminDashboard:
        return 0;
      case AppRoutes.userManagement:
        return 1;
      case AppRoutes.analytics:
        return 2;
      case AppRoutes.profile:
        return 3;
      default:
        return 0;
    }
  }
}
