import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/order_controller.dart';
import '../../models/order.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';
import '../shared/snackbars.dart';

class OrderManagementScreen extends StatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  State<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends State<OrderManagementScreen> with SingleTickerProviderStateMixin {
  final UserController _userController = Get.find<UserController>();
  final OrderController _orderController = Get.find<OrderController>();

  late TabController _tabController;
  final List<String> _tabs = ['Pending', 'Preparing', 'Ready', 'Completed'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    final restaurant = _userController.currentUser;
    if (restaurant != null) {
      await _orderController.loadRestaurantOrders(restaurant.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Order Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOrders,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabs.map((status) => _buildOrderList(status)).toList(),
      ),
    );
  }

  Widget _buildOrderList(String status) {
    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: Obx(() {
        if (_orderController.isLoading) {
          return const LoadingWidget(message: 'Loading orders...');
        }

        if (_orderController.errorMessage.isNotEmpty) {
          return CustomErrorWidget(
            message: _orderController.errorMessage,
            onRetry: _loadOrders,
          );
        }

        final filteredOrders = _orderController.orders
            .where((order) => _getOrderStatusString(order.status) == status.toLowerCase())
            .toList();

        if (filteredOrders.isEmpty) {
          return EmptyStateWidget(
            title: 'No $status Orders',
            message: 'No orders with $status status.',
            icon: Icons.receipt_long,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredOrders.length,
          itemBuilder: (context, index) {
            final order = filteredOrders[index];
            return _buildOrderCard(order);
          },
        );
      }),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order #${order.id.substring(0, 8)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDateTime(order.createdAt),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getOrderStatusColor(order.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getOrderStatusString(order.status).toUpperCase(),
                  style: TextStyle(
                    color: _getOrderStatusColor(order.status),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Customer Info
          Row(
            children: [
              const Icon(Icons.person, size: 16, color: Colors.grey),
              const SizedBox(width: 8),
              Text(
                order.customerName,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const Spacer(),
              const Icon(Icons.phone, size: 16, color: Colors.grey),
              const SizedBox(width: 8),
              Text(
                order.customerPhone,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Delivery Address
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: Colors.grey),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  order.deliveryAddress,
                  style: TextStyle(color: Colors.grey[600]),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Order Items
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Items (${order.items.length})',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                ...order.items.map((item) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              '${item.quantity}x ${item.menuItem.name}',
                              style: const TextStyle(fontSize: 13),
                            ),
                          ),
                          Text(
                            '\$${item.totalPrice.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )),
                const Divider(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Total',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '\$${order.total.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Action Buttons
          _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderModel order) {
    switch (order.status.toString()) {
      case 'pending':
        return Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: 'Reject',
                onPressed: () => _updateOrderStatus(order, 'rejected'),
                textColor: Colors.red,
                borderColor: Colors.red,
                height: 36,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: PrimaryButton(
                text: 'Accept',
                onPressed: () => _updateOrderStatus(order, 'preparing'),
                height: 36,
              ),
            ),
          ],
        );
      case 'preparing':
        return PrimaryButton(
          text: 'Mark as Ready',
          onPressed: () => _updateOrderStatus(order, 'ready'),
          width: double.infinity,
          height: 36,
        );
      case 'ready':
        return PrimaryButton(
          text: 'Mark as Completed',
          onPressed: () => _updateOrderStatus(order, 'completed'),
          width: double.infinity,
          height: 36,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  String _getOrderStatusString(dynamic status) {
    return status.toString().split('.').last;
  }

  Color _getOrderStatusColor(dynamic status) {
    switch (_getOrderStatusString(status)) {
      case 'pending':
        return Colors.orange;
      case 'preparing':
        return Colors.blue;
      case 'ready':
        return Colors.green;
      case 'completed':
        return Colors.grey;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Future<void> _updateOrderStatus(OrderModel order, String newStatus) async {
    try {
      // Convert string status to OrderStatus enum
      OrderStatus? orderStatus = _getOrderStatusFromString(newStatus);
      if (orderStatus == null) {
        if (mounted) {
          ErrorSnackBar.show('Invalid status: $newStatus');
        }
        return;
      }

      // Update order status using the order controller
      final success = await _orderController.updateOrderStatus(order.id, orderStatus);

      if (success) {
        await _loadOrders();
        if (mounted) {
          SuccessSnackBar.show(
            'Order #${order.id.substring(0, 8)} updated to ${_getStatusDisplayName(newStatus)}',
          );
        }
      } else {
        if (mounted) {
          ErrorSnackBar.show(_orderController.errorMessage.isNotEmpty
              ? _orderController.errorMessage
              : 'Failed to update order status');
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorSnackBar.show('Failed to update order status: ${e.toString()}');
      }
    }
  }

  // Helper method to convert string status to OrderStatus enum
  OrderStatus? _getOrderStatusFromString(String statusString) {
    switch (statusString.toLowerCase()) {
      case 'rejected':
        return OrderStatus.cancelled;
      case 'preparing':
        return OrderStatus.preparing;
      case 'ready':
        return OrderStatus.ready;
      case 'completed':
        return OrderStatus.delivered;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'placed':
        return OrderStatus.placed;
      case 'pending':
        return OrderStatus.pending;
      case 'pickedup':
        return OrderStatus.pickedUp;
      case 'ontheway':
        return OrderStatus.onTheWay;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return null;
    }
  }

  // Helper method to get display name for status
  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'rejected':
        return 'Rejected';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready';
      case 'completed':
        return 'Completed';
      default:
        return status.capitalizeFirst ?? status;
    }
  }
}
