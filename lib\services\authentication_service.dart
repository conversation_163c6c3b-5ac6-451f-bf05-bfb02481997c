import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/system_settings.dart';
import '../models/audit_log.dart';
import 'audit_service.dart';

class AuthenticationService extends GetxService {
  // Security settings
  final Rx<SecuritySettings> _securitySettings = SecuritySettings.defaultSettings().obs;

  // Login attempt tracking
  final RxMap<String, LoginAttemptInfo> _loginAttempts = <String, LoginAttemptInfo>{}.obs;

  // Session management
  final RxMap<String, SessionInfo> _activeSessions = <String, SessionInfo>{}.obs;
  Timer? _sessionCleanupTimer;

  // Email verification
  final RxMap<String, EmailVerificationInfo> _emailVerifications = <String, EmailVerificationInfo>{}.obs;

  // Password reset
  final RxMap<String, PasswordResetInfo> _passwordResets = <String, PasswordResetInfo>{}.obs;

  // Dependencies
  late final AuditService _auditService;

  @override
  void onInit() {
    super.onInit();
    _auditService = Get.find<AuditService>();
    _loadSecuritySettings();
    _startSessionCleanup();
    _loadStoredData();
  }

  @override
  void onClose() {
    _sessionCleanupTimer?.cancel();
    super.onClose();
  }

  // Getters
  SecuritySettings get securitySettings => _securitySettings.value;
  Map<String, SessionInfo> get activeSessions => Map.from(_activeSessions);

  // Authentication methods
  Future<AuthResult> authenticateUser(String email, String password, {String? deviceInfo}) async {
    final emailLower = email.toLowerCase();

    // Check if account is locked
    if (_isAccountLocked(emailLower)) {
      final lockInfo = _loginAttempts[emailLower]!;
      final remainingTime = lockInfo.lockoutUntil!.difference(DateTime.now()).inMinutes;

      await _auditService.logAction(
        action: AuditAction.loginFailed,
        entityId: emailLower,
        entityType: 'User',
        reason: 'Login attempt on locked account',
        severity: AuditSeverity.warning,
      );

      return AuthResult.failure('Account locked. Try again in $remainingTime minutes.');
    }

    // Simulate user lookup and password verification
    // In production, this would query the database and verify hashed passwords
    final user = await _findUserByEmail(emailLower);
    if (user == null) {
      await _recordFailedAttempt(emailLower, 'User not found');
      return AuthResult.failure('Invalid email or password');
    }

    // Verify password (in production, compare with hashed password)
    if (!await _verifyPassword(password, user.id)) {
      await _recordFailedAttempt(emailLower, 'Invalid password');
      return AuthResult.failure('Invalid email or password');
    }

    // Check if email verification is required
    if (_securitySettings.value.requireEmailVerification && !user.isEmailVerified) {
      return AuthResult.failure('Please verify your email address before logging in');
    }

    // Clear failed attempts on successful login
    _loginAttempts.remove(emailLower);

    // Create session
    final sessionId = _generateSessionId();
    final session = SessionInfo(
      sessionId: sessionId,
      userId: user.id,
      email: user.email,
      deviceInfo: deviceInfo ?? 'Unknown Device',
      loginTime: DateTime.now(),
      lastActivity: DateTime.now(),
      ipAddress: '***********', // In production, get real IP
    );

    _activeSessions[sessionId] = session;
    await _saveSessionToStorage(session);

    // Log successful login
    await _auditService.logAction(
      action: AuditAction.login,
      entityId: user.id,
      entityType: 'User',
      reason: 'User logged in successfully from ${session.deviceInfo}',
      severity: AuditSeverity.info,
    );

    return AuthResult.success(user, sessionId);
  }

  Future<bool> registerUser({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    String? phone,
    UserRole role = UserRole.customer,
  }) async {
    try {
      // Check if user already exists
      final existingUser = await _findUserByEmail(email.toLowerCase());
      if (existingUser != null) {
        return false;
      }

      // Hash password (simplified for demo)
      _hashPassword(password); // Validate password format

      // Create user (this would be saved to database in production)
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';

      // Send email verification if required
      if (_securitySettings.value.requireEmailVerification) {
        await _sendEmailVerification(email, userId);
      }

      // Log registration
      await _auditService.logAction(
        action: AuditAction.register,
        entityId: userId,
        entityType: 'User',
        reason: 'New user registered: $email',
        severity: AuditSeverity.info,
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> logout(String sessionId) async {
    final session = _activeSessions[sessionId];
    if (session != null) {
      // Log logout
      await _auditService.logAction(
        action: AuditAction.logout,
        entityId: session.userId,
        entityType: 'User',
        reason: 'User logged out from ${session.deviceInfo}',
        severity: AuditSeverity.info,
      );

      _activeSessions.remove(sessionId);
      await _removeSessionFromStorage(sessionId);
    }
  }

  Future<bool> isSessionValid(String sessionId) async {
    final session = _activeSessions[sessionId];
    if (session == null) return false;

    final now = DateTime.now();
    final sessionAge = now.difference(session.lastActivity).inMinutes;

    if (sessionAge > _securitySettings.value.sessionTimeoutMinutes) {
      await logout(sessionId);
      return false;
    }

    // Update last activity
    session.lastActivity = now;
    _activeSessions[sessionId] = session;

    return true;
  }

  Future<bool> sendEmailVerification(String email) async {
    final user = await _findUserByEmail(email.toLowerCase());
    if (user == null) return false;

    return await _sendEmailVerification(email, user.id);
  }

  Future<bool> verifyEmail(String email, String code) async {
    final verification = _emailVerifications[email.toLowerCase()];
    if (verification == null) return false;

    if (verification.expiresAt.isBefore(DateTime.now())) {
      _emailVerifications.remove(email.toLowerCase());
      return false;
    }

    if (verification.code != code) return false;

    // Mark email as verified (in production, update database)
    _emailVerifications.remove(email.toLowerCase());

    await _auditService.logAction(
      action: AuditAction.emailVerified,
      entityId: verification.userId,
      entityType: 'User',
      reason: 'Email verified: $email',
      severity: AuditSeverity.info,
    );

    return true;
  }

  Future<bool> sendPasswordReset(String email) async {
    final user = await _findUserByEmail(email.toLowerCase());
    if (user == null) return false;

    final resetCode = _generateVerificationCode();
    final resetInfo = PasswordResetInfo(
      userId: user.id,
      email: email,
      code: resetCode,
      expiresAt: DateTime.now().add(const Duration(hours: 1)),
    );

    _passwordResets[email.toLowerCase()] = resetInfo;

    // In production, send actual email
    print('Password reset code for $email: $resetCode');

    await _auditService.logAction(
      action: AuditAction.passwordResetRequested,
      entityId: user.id,
      entityType: 'User',
      reason: 'Password reset requested for: $email',
      severity: AuditSeverity.info,
    );

    return true;
  }

  Future<bool> resetPassword(String email, String code, String newPassword) async {
    final resetInfo = _passwordResets[email.toLowerCase()];
    if (resetInfo == null) return false;

    if (resetInfo.expiresAt.isBefore(DateTime.now())) {
      _passwordResets.remove(email.toLowerCase());
      return false;
    }

    if (resetInfo.code != code) return false;

    // Update password (in production, hash and save to database)
    _hashPassword(newPassword); // Validate password format

    _passwordResets.remove(email.toLowerCase());

    await _auditService.logAction(
      action: AuditAction.passwordReset,
      entityId: resetInfo.userId,
      entityType: 'User',
      reason: 'Password reset completed for: $email',
      severity: AuditSeverity.info,
    );

    return true;
  }

  // Private helper methods
  bool _isAccountLocked(String email) {
    final attemptInfo = _loginAttempts[email];
    if (attemptInfo == null) return false;

    return attemptInfo.lockoutUntil != null && attemptInfo.lockoutUntil!.isAfter(DateTime.now());
  }

  Future<void> _recordFailedAttempt(String email, String reason) async {
    final now = DateTime.now();
    final attemptInfo = _loginAttempts[email] ?? LoginAttemptInfo(email: email);

    attemptInfo.failedAttempts++;
    attemptInfo.lastAttempt = now;

    if (attemptInfo.failedAttempts >= _securitySettings.value.maxLoginAttempts) {
      attemptInfo.lockoutUntil = now.add(Duration(minutes: _securitySettings.value.lockoutDurationMinutes));

      await _auditService.logAction(
        action: AuditAction.accountLocked,
        entityId: email,
        entityType: 'User',
        reason: 'Account locked due to too many failed login attempts',
        severity: AuditSeverity.critical,
      );
    }

    _loginAttempts[email] = attemptInfo;

    await _auditService.logAction(
      action: AuditAction.loginFailed,
      entityId: email,
      entityType: 'User',
      reason: 'Failed login attempt: $reason',
      severity: AuditSeverity.warning,
    );
  }

  Future<UserModel?> _findUserByEmail(String email) async {
    // In production, this would query the database
    // For demo, we'll simulate finding a user
    return null; // Will be implemented with actual user lookup
  }

  Future<bool> _verifyPassword(String password, String userId) async {
    // In production, this would compare with hashed password from database
    // For demo, we'll accept any password
    return true;
  }

  String _hashPassword(String password) {
    // Simple hash for demo - in production use bcrypt or similar
    final bytes = utf8.encode(password + 'salt');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  String _generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  Future<bool> _sendEmailVerification(String email, String userId) async {
    final code = _generateVerificationCode();
    final verification = EmailVerificationInfo(
      userId: userId,
      email: email,
      code: code,
      expiresAt: DateTime.now().add(const Duration(hours: 24)),
    );

    _emailVerifications[email.toLowerCase()] = verification;

    // In production, send actual email
    print('Email verification code for $email: $code');

    return true;
  }

  void _startSessionCleanup() {
    _sessionCleanupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupExpiredSessions();
    });
  }

  void _cleanupExpiredSessions() {
    final now = DateTime.now();
    final expiredSessions = <String>[];

    for (final entry in _activeSessions.entries) {
      final sessionAge = now.difference(entry.value.lastActivity).inMinutes;
      if (sessionAge > _securitySettings.value.sessionTimeoutMinutes) {
        expiredSessions.add(entry.key);
      }
    }

    for (final sessionId in expiredSessions) {
      _activeSessions.remove(sessionId);
      _removeSessionFromStorage(sessionId);
    }
  }

  Future<void> _loadSecuritySettings() async {
    // In production, load from database or config
    _securitySettings.value = SecuritySettings.defaultSettings();
  }

  Future<void> _loadStoredData() async {
    // Load active sessions from storage
    // Implementation would restore sessions from persistent storage
  }

  Future<void> _saveSessionToStorage(SessionInfo session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('session_${session.sessionId}', jsonEncode(session.toJson()));
  }

  Future<void> _removeSessionFromStorage(String sessionId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('session_$sessionId');
  }
}

// Supporting classes
class AuthResult {
  final bool success;
  final String? error;
  final UserModel? user;
  final String? sessionId;

  AuthResult.success(this.user, this.sessionId)
      : success = true,
        error = null;
  AuthResult.failure(this.error)
      : success = false,
        user = null,
        sessionId = null;
}

class LoginAttemptInfo {
  final String email;
  int failedAttempts;
  DateTime? lastAttempt;
  DateTime? lockoutUntil;

  LoginAttemptInfo({
    required this.email,
    this.failedAttempts = 0,
    this.lastAttempt,
    this.lockoutUntil,
  });
}

class SessionInfo {
  final String sessionId;
  final String userId;
  final String email;
  final String deviceInfo;
  final DateTime loginTime;
  DateTime lastActivity;
  final String ipAddress;

  SessionInfo({
    required this.sessionId,
    required this.userId,
    required this.email,
    required this.deviceInfo,
    required this.loginTime,
    required this.lastActivity,
    required this.ipAddress,
  });

  Map<String, dynamic> toJson() => {
        'sessionId': sessionId,
        'userId': userId,
        'email': email,
        'deviceInfo': deviceInfo,
        'loginTime': loginTime.toIso8601String(),
        'lastActivity': lastActivity.toIso8601String(),
        'ipAddress': ipAddress,
      };

  factory SessionInfo.fromJson(Map<String, dynamic> json) => SessionInfo(
        sessionId: json['sessionId'],
        userId: json['userId'],
        email: json['email'],
        deviceInfo: json['deviceInfo'],
        loginTime: DateTime.parse(json['loginTime']),
        lastActivity: DateTime.parse(json['lastActivity']),
        ipAddress: json['ipAddress'],
      );
}

class EmailVerificationInfo {
  final String userId;
  final String email;
  final String code;
  final DateTime expiresAt;

  EmailVerificationInfo({
    required this.userId,
    required this.email,
    required this.code,
    required this.expiresAt,
  });
}

class PasswordResetInfo {
  final String userId;
  final String email;
  final String code;
  final DateTime expiresAt;

  PasswordResetInfo({
    required this.userId,
    required this.email,
    required this.code,
    required this.expiresAt,
  });
}
