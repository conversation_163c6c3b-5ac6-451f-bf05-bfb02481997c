import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_item.dart';
import '../models/menu_item.dart';
import '../models/user.dart';
import '../core/app_constants.dart';
import 'user_controller.dart';
import 'restaurant_controller.dart';

class CartController extends GetxController {
  // Observable variables
  final RxList<CartItem> _cartItems = <CartItem>[].obs;
  final Rx<String?> _currentRestaurantId = Rx<String?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxDouble _subtotal = 0.0.obs;
  final RxDouble _tax = 0.0.obs;
  final RxDouble _deliveryFee = 0.0.obs;
  final RxDouble _total = 0.0.obs;

  // Constants from AppConstants
  static double get taxRate => AppConstants.taxRate;
  static double get baseDeliveryFee => AppConstants.baseDeliveryFee;
  static double get freeDeliveryThreshold => AppConstants.freeDeliveryThreshold;

  // Dependencies
  final UserController _userController = Get.find<UserController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();

  // Getters
  List<CartItem> get cartItems => _cartItems;
  String? get currentRestaurantId => _currentRestaurantId.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  double get subtotal => _subtotal.value;
  double get tax => _tax.value;
  double get deliveryFee => _deliveryFee.value;
  double get total => _total.value;
  int get itemCount => _cartItems.fold(0, (sum, item) => sum + item.quantity);
  bool get isEmpty => _cartItems.isEmpty;
  bool get isNotEmpty => _cartItems.isNotEmpty;
  UserModel? get currentRestaurant =>
      _restaurantController.restaurants.firstWhereOrNull((r) => r.id == _currentRestaurantId.value);

  @override
  void onInit() {
    super.onInit();
    _loadCartFromStorage();
    _setupCalculationListeners();
  }

  // Cart management
  Future<bool> addItem(MenuItem menuItem, {int quantity = 1, List<String>? specialInstructions}) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Check if user is logged in
      if (!_userController.isLoggedIn) {
        _errorMessage.value = 'Please log in to add items to cart';
        return false;
      }

      // Get restaurant ID for this menu item
      final restaurantId = _getRestaurantIdForMenuItem(menuItem.id);
      if (restaurantId.isEmpty) {
        _errorMessage.value = 'Unable to determine restaurant for this item';
        return false;
      }

      // Check if cart is empty or from same restaurant
      if (_cartItems.isNotEmpty && _currentRestaurantId.value != restaurantId) {
        // Ask user if they want to clear cart and start fresh
        final shouldClear = await _showRestaurantChangeDialog();
        if (shouldClear) {
          await clearCart();
        } else {
          return false;
        }
      }

      // Set current restaurant
      _currentRestaurantId.value = restaurantId;

      // Check if item already exists in cart
      final existingItemIndex = _cartItems.indexWhere(
        (item) => item.menuItem.id == menuItem.id && item.specialInstructions == (specialInstructions ?? ''),
      );

      if (existingItemIndex != -1) {
        // Update quantity of existing item
        final existingItem = _cartItems[existingItemIndex];
        final updatedItem = CartItem(
          id: existingItem.id,
          menuItem: existingItem.menuItem,
          quantity: existingItem.quantity + quantity,
          specialInstructions: existingItem.specialInstructions,
          restaurantId: existingItem.restaurantId,
        );
        _cartItems[existingItemIndex] = updatedItem;
      } else {
        // Add new item to cart
        final cartItem = CartItem(
          id: 'cart_${DateTime.now().millisecondsSinceEpoch}',
          menuItem: menuItem,
          quantity: quantity,
          specialInstructions: specialInstructions ?? [],
          restaurantId: restaurantId,
        );
        _cartItems.add(cartItem);
      }

      await _saveCartToStorage();
      _calculateTotals();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to add item to cart: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> removeItem(String cartItemId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      _cartItems.removeWhere((item) => item.id == cartItemId);

      // Clear restaurant if cart is empty
      if (_cartItems.isEmpty) {
        _currentRestaurantId.value = null;
      }

      await _saveCartToStorage();
      _calculateTotals();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to remove item from cart: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateQuantity(String cartItemId, int newQuantity) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (newQuantity <= 0) {
        return await removeItem(cartItemId);
      }

      final itemIndex = _cartItems.indexWhere((item) => item.id == cartItemId);
      if (itemIndex == -1) {
        _errorMessage.value = 'Item not found in cart';
        return false;
      }

      final existingItem = _cartItems[itemIndex];
      final updatedItem = CartItem(
        id: existingItem.id,
        menuItem: existingItem.menuItem,
        quantity: newQuantity,
        specialInstructions: existingItem.specialInstructions,
        restaurantId: existingItem.restaurantId,
      );

      _cartItems[itemIndex] = updatedItem;

      await _saveCartToStorage();
      _calculateTotals();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update item quantity: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateSpecialInstructions(String cartItemId, List<String> instructions) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final itemIndex = _cartItems.indexWhere((item) => item.id == cartItemId);
      if (itemIndex == -1) {
        _errorMessage.value = 'Item not found in cart';
        return false;
      }

      final existingItem = _cartItems[itemIndex];
      final updatedItem = CartItem(
        id: existingItem.id,
        menuItem: existingItem.menuItem,
        quantity: existingItem.quantity,
        specialInstructions: instructions,
        restaurantId: existingItem.restaurantId,
      );

      _cartItems[itemIndex] = updatedItem;

      await _saveCartToStorage();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update special instructions: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> clearCart() async {
    try {
      _isLoading.value = true;

      _cartItems.clear();
      _currentRestaurantId.value = null;

      await _saveCartToStorage();
      _calculateTotals();
    } catch (e) {
      _errorMessage.value = 'Failed to clear cart: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Cart calculations
  void _calculateTotals() {
    // Calculate subtotal
    _subtotal.value = _cartItems.fold(0.0, (sum, item) => sum + (item.menuItem.price * item.quantity));

    // Calculate tax
    _tax.value = _subtotal.value * taxRate;

    // Calculate delivery fee
    _deliveryFee.value = _subtotal.value >= freeDeliveryThreshold ? 0.0 : baseDeliveryFee;

    // Calculate total
    _total.value = _subtotal.value + _tax.value + _deliveryFee.value;
  }

  // Cart validation
  bool validateCart() {
    if (_cartItems.isEmpty) {
      _errorMessage.value = 'Cart is empty';
      return false;
    }

    if (_currentRestaurantId.value == null) {
      _errorMessage.value = 'No restaurant selected';
      return false;
    }

    if (!_userController.isLoggedIn) {
      _errorMessage.value = 'Please log in to proceed';
      return false;
    }

    // Check if all items are still available
    for (final cartItem in _cartItems) {
      if (!cartItem.menuItem.isAvailable) {
        _errorMessage.value = '${cartItem.menuItem.name} is no longer available';
        return false;
      }
    }

    return true;
  }

  // Cart persistence
  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartData = {
        'items': _cartItems.map((item) => item.toJson()).toList(),
        'restaurantId': _currentRestaurantId.value,
      };
      await prefs.setString('cart_data', jsonEncode(cartData));
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'Failed to save cart: ${e.toString()}',
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _loadCartFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartDataString = prefs.getString('cart_data');

      if (cartDataString != null) {
        final cartData = jsonDecode(cartDataString);

        _currentRestaurantId.value = cartData['restaurantId'];

        final itemsList = cartData['items'] as List;
        _cartItems.clear();
        _cartItems.addAll(
          itemsList.map((itemJson) => CartItem.fromJson(itemJson)).toList(),
        );

        _calculateTotals();
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'Failed to load cart: ${e.toString()}',
          duration: const Duration(seconds: 3),
        ),
      );
      // Clear corrupted data
      await clearCart();
    }
  }

  // Helper methods
  void _setupCalculationListeners() {
    // Recalculate totals whenever cart items change
    ever(_cartItems, (_) => _calculateTotals());
  }

  Future<bool> _showRestaurantChangeDialog() async {
    // In a real app, this would show a dialog to the user
    // For now, we'll return true to allow restaurant change
    return true;
  }

  String _getRestaurantIdForMenuItem(String menuItemId) {
    // Simple mapping for demo purposes
    // In a real app, this would be stored in the menu item model
    if (['menu_001', 'menu_002', 'menu_003'].contains(menuItemId)) {
      return 'rest_001'; // Tony's Pizzeria
    } else if (['menu_004', 'menu_005', 'menu_006'].contains(menuItemId)) {
      return 'rest_002'; // Burger Palace
    }
    return '';
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  CartItem? getCartItemById(String cartItemId) {
    return _cartItems.firstWhereOrNull((item) => item.id == cartItemId);
  }

  int getItemQuantityInCart(String menuItemId) {
    return _cartItems.where((item) => item.menuItem.id == menuItemId).fold(0, (sum, item) => sum + item.quantity);
  }

  bool isItemInCart(String menuItemId) {
    return _cartItems.any((item) => item.menuItem.id == menuItemId);
  }

  double getItemTotalPrice(String cartItemId) {
    final item = getCartItemById(cartItemId);
    if (item == null) return 0.0;
    return item.menuItem.price * item.quantity;
  }

  Map<String, dynamic> getCartSummary() {
    return {
      'itemCount': itemCount,
      'subtotal': subtotal,
      'tax': tax,
      'deliveryFee': deliveryFee,
      'total': total,
      'restaurantId': currentRestaurantId,
      'restaurantName':
          currentRestaurant != null ? '${currentRestaurant!.firstName} ${currentRestaurant!.lastName}' : null,
    };
  }

  // Quick actions
  Future<void> incrementItemQuantity(String cartItemId) async {
    final item = getCartItemById(cartItemId);
    if (item != null) {
      await updateQuantity(cartItemId, item.quantity + 1);
    }
  }

  Future<void> decrementItemQuantity(String cartItemId) async {
    final item = getCartItemById(cartItemId);
    if (item != null) {
      await updateQuantity(cartItemId, item.quantity - 1);
    }
  }
}
