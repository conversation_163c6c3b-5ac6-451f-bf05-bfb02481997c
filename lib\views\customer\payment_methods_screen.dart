import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/payment_controller.dart';
import '../../models/order.dart';
import '../shared/custom_app_bar.dart';
import '../shared/loading_widget.dart';
import '../shared/empty_state_widget.dart';
import '../shared/error_widget.dart';
import '../shared/snackbars.dart';

class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  final PaymentController _paymentController = Get.find<PaymentController>();

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    await _paymentController.loadPaymentMethods();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Payment Methods',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddPaymentMethodDialog,
          ),
        ],
      ),
      body: Obx(() {
        if (_paymentController.isLoading) {
          return const LoadingWidget(message: 'Loading payment methods...');
        }

        if (_paymentController.errorMessage.isNotEmpty) {
          return CustomErrorWidget(
            message: _paymentController.errorMessage,
            onRetry: _loadPaymentMethods,
          );
        }

        final paymentMethods = _paymentController.paymentMethods;
        if (paymentMethods.isEmpty) {
          return const EmptyStateWidget(
            title: 'No Payment Methods',
            message: 'Add a payment method to make ordering easier.',
            icon: Icons.payment_outlined,
            actionText: 'Add Payment Method',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: paymentMethods.length,
          itemBuilder: (context, index) {
            final paymentMethod = paymentMethods[index];
            return _buildPaymentMethodCard(paymentMethod);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddPaymentMethodDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod paymentMethod) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Payment Method Icon
            Container(
              width: 50,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getPaymentMethodIcon(paymentMethod.type),
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            // Payment Method Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        paymentMethod.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (paymentMethod.isDefault) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Default',
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (paymentMethod.cardNumber != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      paymentMethod.maskedCardNumber,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  if (paymentMethod.expiryDate != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      'Expires ${paymentMethod.expiryDate}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // Actions
            PopupMenuButton<String>(
              onSelected: (value) => _handlePaymentMethodAction(value, paymentMethod),
              itemBuilder: (context) => [
                if (!paymentMethod.isDefault)
                  const PopupMenuItem(
                    value: 'default',
                    child: Row(
                      children: [
                        Icon(Icons.star, size: 20),
                        SizedBox(width: 8),
                        Text('Set as Default'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 20),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPaymentMethodIcon(PaymentType type) {
    switch (type) {
      case PaymentType.creditCard:
      case PaymentType.debitCard:
        return Icons.credit_card;
      case PaymentType.paypal:
        return Icons.account_balance_wallet;
      case PaymentType.applePay:
      case PaymentType.googlePay:
        return Icons.phone_android;
      case PaymentType.cash:
        return Icons.money;
    }
  }

  void _handlePaymentMethodAction(String action, PaymentMethod paymentMethod) {
    switch (action) {
      case 'default':
        _setDefaultPaymentMethod(paymentMethod);
        break;
      case 'edit':
        _showEditPaymentMethodDialog(paymentMethod);
        break;
      case 'delete':
        _showDeleteConfirmation(paymentMethod);
        break;
    }
  }

  Future<void> _setDefaultPaymentMethod(PaymentMethod paymentMethod) async {
    final success = await _paymentController.setDefaultPaymentMethod(paymentMethod.id);
    if (success) {
      SuccessSnackBar.show('Default payment method updated!');
    } else {
      ErrorSnackBar.show(
        _paymentController.errorMessage.isNotEmpty
            ? _paymentController.errorMessage
            : 'Failed to update default payment method',
      );
    }
  }

  void _showAddPaymentMethodDialog() {
    _showPaymentMethodDialog();
  }

  void _showEditPaymentMethodDialog(PaymentMethod paymentMethod) {
    _showPaymentMethodDialog(paymentMethod: paymentMethod);
  }

  void _showPaymentMethodDialog({PaymentMethod? paymentMethod}) {
    final isEditing = paymentMethod != null;
    PaymentType selectedType = paymentMethod?.type ?? PaymentType.creditCard;
    final displayNameController = TextEditingController(text: paymentMethod?.displayName ?? '');
    final cardNumberController = TextEditingController(text: paymentMethod?.cardNumber ?? '');
    final expiryController = TextEditingController(text: paymentMethod?.expiryDate ?? '');
    final holderNameController = TextEditingController(text: paymentMethod?.cardHolderName ?? '');

    Get.dialog(
      AlertDialog(
        title: Text(isEditing ? 'Edit Payment Method' : 'Add Payment Method'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<PaymentType>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'Payment Type',
                  border: OutlineInputBorder(),
                ),
                items: PaymentType.values
                    .map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: displayNameController,
                decoration: const InputDecoration(
                  labelText: 'Display Name',
                  border: OutlineInputBorder(),
                ),
              ),
              if (selectedType == PaymentType.creditCard || selectedType == PaymentType.debitCard) ...[
                const SizedBox(height: 16),
                TextField(
                  controller: cardNumberController,
                  decoration: const InputDecoration(
                    labelText: 'Last 4 Digits',
                    border: OutlineInputBorder(),
                    counterText: '',
                  ),
                  keyboardType: TextInputType.number,
                  maxLength: 4,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: expiryController,
                  decoration: const InputDecoration(
                    labelText: 'Expiry Date (MM/YY)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.datetime,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: holderNameController,
                  decoration: const InputDecoration(
                    labelText: 'Cardholder Name',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (displayNameController.text.trim().isEmpty) {
                ErrorSnackBar.show('Please enter a display name');
                return;
              }

              final newPaymentMethod = PaymentMethod(
                id: isEditing ? paymentMethod.id : 'pm_${DateTime.now().millisecondsSinceEpoch}',
                type: selectedType,
                displayName: displayNameController.text.trim(),
                cardNumber: cardNumberController.text.trim().isEmpty ? null : cardNumberController.text.trim(),
                expiryDate: expiryController.text.trim().isEmpty ? null : expiryController.text.trim(),
                cardHolderName: holderNameController.text.trim().isEmpty ? null : holderNameController.text.trim(),
                isDefault: paymentMethod?.isDefault ?? false,
              );

              bool success;
              if (isEditing) {
                success = await _paymentController.updatePaymentMethod(newPaymentMethod);
              } else {
                success = await _paymentController.addPaymentMethod(newPaymentMethod);
              }

              if (!mounted) return;
              Get.back();

              if (success) {
                SuccessSnackBar.show(isEditing ? 'Payment method updated!' : 'Payment method added!');
              } else {
                ErrorSnackBar.show(
                  _paymentController.errorMessage.isNotEmpty
                      ? _paymentController.errorMessage
                      : 'Failed to ${isEditing ? 'update' : 'add'} payment method',
                );
              }
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(PaymentMethod paymentMethod) {
    if (paymentMethod.isDefault) {
      ErrorSnackBar.show('Cannot delete default payment method');
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Delete Payment Method'),
        content: Text('Are you sure you want to delete ${paymentMethod.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final success = await _paymentController.deletePaymentMethod(paymentMethod.id);
              if (!mounted) return;
              Get.back();

              if (success) {
                SuccessSnackBar.show('Payment method deleted!');
              } else {
                ErrorSnackBar.show(
                  _paymentController.errorMessage.isNotEmpty
                      ? _paymentController.errorMessage
                      : 'Failed to delete payment method',
                );
              }
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
