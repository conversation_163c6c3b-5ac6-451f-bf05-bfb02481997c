import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/order.dart';
import '../services/demo_data_service.dart';
import 'user_controller.dart';
import 'cart_controller.dart';
import 'restaurant_controller.dart';

class OrderController extends GetxController {
  // Observable variables
  final RxList<OrderModel> _orders = <OrderModel>[].obs;
  final RxList<OrderModel> _userOrders = <OrderModel>[].obs;
  final Rx<OrderModel?> _currentOrder = Rx<OrderModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _selectedStatus = 'all'.obs;

  // Dependencies
  final UserController _userController = Get.find<UserController>();
  final CartController _cartController = Get.find<CartController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();

  // Getters
  List<OrderModel> get orders => _orders;
  List<OrderModel> get userOrders => _userOrders;
  OrderModel? get currentOrder => _currentOrder.value;
  bool get isLoading => _isLoading.value;
  bool get hasError => _errorMessage.value.isNotEmpty;
  String get errorMessage => _errorMessage.value;
  String get selectedStatus => _selectedStatus.value;

  @override
  void onInit() {
    super.onInit();
    _loadOrdersFromStorage();
    _loadDemoOrders();
    _setupUserOrdersListener();
  }

  // Order placement
  Future<OrderModel?> placeOrder({
    required PaymentMethod paymentMethod,
    String? specialInstructions,
    String? deliveryAddress,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate cart
      if (!_cartController.validateCart()) {
        _errorMessage.value = _cartController.errorMessage;
        return null;
      }

      // Validate user
      if (!_userController.isLoggedIn || _userController.currentUser == null) {
        _errorMessage.value = 'User not logged in';
        return null;
      }

      // Create order
      final now = DateTime.now();
      final user = _userController.currentUser!;
      final cartSubtotal = _cartController.subtotal;
      final taxAmount = cartSubtotal * 0.08; // 8% tax

      final order = OrderModel(
        id: 'order_${now.millisecondsSinceEpoch}',
        items: List.from(_cartController.cartItems),
        restaurantId: _cartController.currentRestaurantId!,
        deliveryFee: _cartController.deliveryFee,
        status: OrderStatus.placed,
        paymentMethod: paymentMethod,
        placedAt: now,
        specialInstructions: specialInstructions,
        estimatedDeliveryTime: now.add(const Duration(minutes: 45)),
        createdAt: now,
        customerName: '${user.firstName} ${user.lastName}',
        customerPhone: user.phone ?? '',
        deliveryAddress: deliveryAddress ?? 'Default Address',
        notes: specialInstructions ?? '',
        subtotal: cartSubtotal,
        tax: taxAmount,
        paymentStatus: PaymentStatus.pending,
        restaurantName: _getRestaurantName(),
      );

      // Add to orders list
      _orders.add(order);
      _currentOrder.value = order;

      // Clear cart
      await _cartController.clearCart();

      // Save to storage
      await _saveOrdersToStorage();

      // Update user orders
      _updateUserOrders();

      return order;
    } catch (e) {
      _errorMessage.value = 'Failed to place order: ${e.toString()}';
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  // Order management
  Future<bool> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) {
        _errorMessage.value = 'Order not found';
        return false;
      }

      final existingOrder = _orders[orderIndex];

      // Validate status transition
      if (!_isValidStatusTransition(existingOrder.status, newStatus)) {
        _errorMessage.value = 'Invalid status transition';
        return false;
      }

      // Update order using copyWith
      final updatedOrder = existingOrder.copyWith(
        status: newStatus,
        deliveredAt: newStatus == OrderStatus.delivered ? DateTime.now() : null,
      );

      _orders[orderIndex] = updatedOrder;

      // Update current order if it's the same
      if (_currentOrder.value?.id == orderId) {
        _currentOrder.value = updatedOrder;
      }

      await _saveOrdersToStorage();
      _updateUserOrders();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update order status: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final order = _orders.firstWhereOrNull((o) => o.id == orderId);
      if (order == null) {
        _errorMessage.value = 'Order not found';
        return false;
      }

      // Check if order can be cancelled
      if (!_canCancelOrder(order.status)) {
        _errorMessage.value = 'Order cannot be cancelled at this stage';
        return false;
      }

      // Update order status to cancelled
      final success = await updateOrderStatus(orderId, OrderStatus.cancelled);

      // In a real app, you would process refunds here

      return success;
    } catch (e) {
      _errorMessage.value = 'Failed to cancel order: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Order retrieval
  Future<void> loadUserOrders() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (!_userController.isLoggedIn || _userController.currentUser == null) {
        _errorMessage.value = 'User not logged in';
        return;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _updateUserOrders();
    } catch (e) {
      _errorMessage.value = 'Failed to load orders: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadOrderById(String orderId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 300));

      final order = _orders.firstWhereOrNull((o) => o.id == orderId);
      if (order != null) {
        _currentOrder.value = order;
      } else {
        _errorMessage.value = 'Order not found';
      }
    } catch (e) {
      _errorMessage.value = 'Failed to load order: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Filtering and searching
  void setStatusFilter(String status) {
    _selectedStatus.value = status;
    _updateUserOrders();
  }

  List<OrderModel> getOrdersByStatus(OrderStatus status) {
    return _userOrders.where((order) => order.status == status).toList();
  }

  List<OrderModel> getOrdersByRestaurant(String restaurantId) {
    return _userOrders.where((order) => order.restaurantId == restaurantId).toList();
  }

  List<OrderModel> getOrdersByDateRange(DateTime start, DateTime end) {
    return _userOrders.where((order) => order.placedAt.isAfter(start) && order.placedAt.isBefore(end)).toList();
  }

  // Restaurant and driver specific methods
  List<OrderModel> getRestaurantOrders(String restaurantId) {
    return _orders.where((order) => order.restaurantId == restaurantId).toList();
  }

  List<OrderModel> getOrdersForDelivery() {
    return _orders
        .where((order) =>
            order.status == OrderStatus.ready ||
            order.status == OrderStatus.pickedUp ||
            order.status == OrderStatus.onTheWay)
        .toList();
  }

  Future<bool> assignDriverToOrder(String orderId, String driverId) async {
    // This would be implemented for driver assignment
    // For now, just update the order status to picked up
    return await updateOrderStatus(orderId, OrderStatus.pickedUp);
  }

  // Analytics and statistics
  Map<String, dynamic> getOrderStats() {
    final userOrdersList = _userOrders;

    if (userOrdersList.isEmpty) {
      return {
        'totalOrders': 0,
        'totalSpent': 0.0,
        'averageOrderValue': 0.0,
        'favoriteRestaurant': null,
      };
    }

    final totalSpent = userOrdersList.fold(0.0, (sum, order) => sum + _calculateOrderTotal(order));
    final averageOrderValue = totalSpent / userOrdersList.length;

    // Find favorite restaurant
    final restaurantCounts = <String, int>{};
    for (final order in userOrdersList) {
      restaurantCounts[order.restaurantId] = (restaurantCounts[order.restaurantId] ?? 0) + 1;
    }

    String? favoriteRestaurant;
    int maxCount = 0;
    restaurantCounts.forEach((restaurantId, count) {
      if (count > maxCount) {
        maxCount = count;
        favoriteRestaurant = restaurantId;
      }
    });

    return {
      'totalOrders': userOrdersList.length,
      'totalSpent': totalSpent,
      'averageOrderValue': averageOrderValue,
      'favoriteRestaurant': favoriteRestaurant,
    };
  }

  // Private methods
  void _setupUserOrdersListener() {
    // Update user orders when user changes
    ever(_userController.currentUser.obs, (_) => _updateUserOrders());
  }

  void _updateUserOrders() {
    if (!_userController.isLoggedIn || _userController.currentUser == null) {
      _userOrders.clear();
      return;
    }

    // For demo purposes, we'll assume all orders belong to the current user
    // In a real app, orders would have a userId field
    var filteredOrders = List<OrderModel>.from(_orders);

    // Apply status filter
    if (_selectedStatus.value != 'all') {
      final status = _getOrderStatusFromString(_selectedStatus.value);
      if (status != null) {
        filteredOrders = filteredOrders.where((order) => order.status == status).toList();
      }
    }

    // Sort by order date (newest first)
    filteredOrders.sort((a, b) => b.placedAt.compareTo(a.placedAt));

    _userOrders.clear();
    _userOrders.addAll(filteredOrders);
  }

  bool _isValidStatusTransition(OrderStatus current, OrderStatus next) {
    // Define valid status transitions
    const validTransitions = {
      OrderStatus.placed: [OrderStatus.confirmed, OrderStatus.cancelled],
      OrderStatus.confirmed: [OrderStatus.preparing, OrderStatus.cancelled],
      OrderStatus.preparing: [OrderStatus.ready, OrderStatus.cancelled],
      OrderStatus.ready: [OrderStatus.pickedUp],
      OrderStatus.pickedUp: [OrderStatus.onTheWay],
      OrderStatus.onTheWay: [OrderStatus.delivered],
      OrderStatus.delivered: [], // Final state
      OrderStatus.cancelled: [], // Final state
    };

    return validTransitions[current]?.contains(next) ?? false;
  }

  bool _canCancelOrder(OrderStatus status) {
    return [
      OrderStatus.placed,
      OrderStatus.confirmed,
      OrderStatus.preparing,
    ].contains(status);
  }

  OrderStatus? _getOrderStatusFromString(String statusString) {
    switch (statusString.toLowerCase()) {
      case 'placed':
        return OrderStatus.placed;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'preparing':
        return OrderStatus.preparing;
      case 'ready':
        return OrderStatus.ready;
      case 'pickedup':
        return OrderStatus.pickedUp;
      case 'ontheway':
        return OrderStatus.onTheWay;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return null;
    }
  }

  Future<void> _saveOrdersToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersData = _orders.map((order) => order.toJson()).toList();
      await prefs.setString('orders_data', jsonEncode(ordersData));
    } catch (e) {
      // In production, use a proper logging framework
      _errorMessage.value = 'Error saving orders: ${e.toString()}';
    }
  }

  Future<void> _loadOrdersFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersDataString = prefs.getString('orders_data');

      if (ordersDataString != null) {
        final ordersData = jsonDecode(ordersDataString) as List;
        _orders.clear();
        _orders.addAll(
          ordersData.map((orderJson) => OrderModel.fromJson(orderJson)).toList(),
        );
        _updateUserOrders();
      }
    } catch (e) {
      // In production, use a proper logging framework
      _errorMessage.value = 'Error loading orders: ${e.toString()}';
    }
  }

  Future<void> _loadDemoOrders() async {
    try {
      // Load demo orders from JSON file
      await DemoDataService.instance.loadDemoData();
      final orders = DemoDataService.instance.getOrders();
      _orders.addAll(orders);
      _updateUserOrders();
    } catch (e) {
      // Fallback - no demo orders if JSON loading fails
      // In production, use a proper logging framework
      _errorMessage.value = 'Error loading demo orders: ${e.toString()}';
    }
  }

  // Helper methods
  double _calculateOrderTotal(OrderModel order) {
    final itemsTotal = order.items.fold(0.0, (sum, item) => sum + (item.menuItem.price * item.quantity));
    return itemsTotal + order.deliveryFee;
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  String getOrderStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.placed:
        return 'Order Placed';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.onTheWay:
        return 'On the Way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String getEstimatedDeliveryText(OrderModel order) {
    if (order.estimatedDeliveryTime == null) return 'TBD';

    final now = DateTime.now();
    final estimated = order.estimatedDeliveryTime!;

    if (estimated.isBefore(now)) {
      return 'Overdue';
    }

    final difference = estimated.difference(now);
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} mins';
    } else {
      return '${difference.inHours}h ${difference.inMinutes % 60}m';
    }
  }

  bool canTrackOrder(OrderModel order) {
    return [
      OrderStatus.confirmed,
      OrderStatus.preparing,
      OrderStatus.ready,
      OrderStatus.pickedUp,
      OrderStatus.onTheWay,
    ].contains(order.status);
  }

  bool canCancelOrder(OrderModel order) {
    return _canCancelOrder(order.status);
  }

  // Enhanced tracking functionality
  Map<String, dynamic> getOrderTrackingInfo(OrderModel order) {
    final now = DateTime.now();
    final estimatedDelivery = order.estimatedDeliveryTime;

    // Calculate progress percentage based on status
    double progressPercentage = 0.0;
    switch (order.status) {
      case OrderStatus.pending:
        progressPercentage = 0.1;
        break;
      case OrderStatus.placed:
        progressPercentage = 0.2;
        break;
      case OrderStatus.confirmed:
        progressPercentage = 0.3;
        break;
      case OrderStatus.preparing:
        progressPercentage = 0.5;
        break;
      case OrderStatus.ready:
        progressPercentage = 0.7;
        break;
      case OrderStatus.pickedUp:
        progressPercentage = 0.8;
        break;
      case OrderStatus.onTheWay:
        progressPercentage = 0.9;
        break;
      case OrderStatus.delivered:
        progressPercentage = 1.0;
        break;
      case OrderStatus.cancelled:
        progressPercentage = 0.0;
        break;
    }

    // Calculate estimated time remaining
    String timeRemaining = 'Calculating...';
    if (estimatedDelivery != null) {
      if (order.status == OrderStatus.delivered) {
        timeRemaining = 'Delivered';
      } else if (order.status == OrderStatus.cancelled) {
        timeRemaining = 'Cancelled';
      } else if (estimatedDelivery.isBefore(now)) {
        timeRemaining = 'Overdue';
      } else {
        final difference = estimatedDelivery.difference(now);
        if (difference.inMinutes < 60) {
          timeRemaining = '${difference.inMinutes} mins remaining';
        } else {
          timeRemaining = '${difference.inHours}h ${difference.inMinutes % 60}m remaining';
        }
      }
    }

    return {
      'progressPercentage': progressPercentage,
      'timeRemaining': timeRemaining,
      'currentStatus': getOrderStatusDisplayName(order.status),
      'canTrack': canTrackOrder(order),
      'canCancel': canCancelOrder(order),
      'estimatedDelivery': estimatedDelivery,
      'isOverdue':
          estimatedDelivery != null && estimatedDelivery.isBefore(now) && order.status != OrderStatus.delivered,
    };
  }

  // Load available orders for drivers
  Future<void> loadAvailableOrders() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // This would typically load orders that are ready for pickup
      // For now, we'll use existing orders with ready status
      // Update the orders list if needed
      update();
    } catch (e) {
      _errorMessage.value = 'Failed to load available orders: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Load restaurant-specific orders
  Future<void> loadRestaurantOrders([String? restaurantId]) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // This would typically filter orders for the current restaurant
      // For now, we'll use all orders
      update();
    } catch (e) {
      _errorMessage.value = 'Failed to load restaurant orders: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Create order from cart (used by checkout screen)
  Future<OrderModel?> createOrderFromCart({
    required String deliveryAddress,
    required PaymentMethod paymentMethod,
    String? notes,
  }) async {
    return await placeOrder(
      paymentMethod: paymentMethod,
      specialInstructions: notes,
      deliveryAddress: deliveryAddress,
    );
  }

  // Helper method to get restaurant name from cart
  String _getRestaurantName() {
    final currentRestaurant = _cartController.currentRestaurant;
    if (currentRestaurant != null) {
      return '${currentRestaurant.firstName} ${currentRestaurant.lastName}';
    }

    // Fallback: try to get restaurant from cart items
    if (_cartController.cartItems.isNotEmpty) {
      final restaurantId = _cartController.cartItems.first.restaurantId;
      final restaurant = _restaurantController.restaurants.firstWhereOrNull((r) => r.id == restaurantId);
      if (restaurant != null) {
        return '${restaurant.firstName} ${restaurant.lastName}';
      }
    }

    return 'Restaurant'; // Fallback
  }
}
