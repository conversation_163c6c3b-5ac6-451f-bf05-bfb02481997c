import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/order.dart';
import '../services/demo_data_service.dart';
import 'user_controller.dart';

class PaymentController extends GetxController {
  // Observable variables
  final RxList<PaymentMethod> _paymentMethods = <PaymentMethod>[].obs;
  final Rx<PaymentMethod?> _selectedPaymentMethod = Rx<PaymentMethod?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<PaymentResult> _paymentHistory = <PaymentResult>[].obs;

  // Dependencies
  final UserController _userController = Get.find<UserController>();

  // Getters
  List<PaymentMethod> get paymentMethods => _paymentMethods;
  PaymentMethod? get selectedPaymentMethod => _selectedPaymentMethod.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  List<PaymentResult> get paymentHistory => _paymentHistory;
  PaymentMethod? get defaultPaymentMethod => _paymentMethods.firstWhereOrNull((method) => method.isDefault);

  @override
  void onInit() {
    super.onInit();
    _loadPaymentMethodsFromStorage();
    _loadDemoPaymentMethods();
    _setupUserListener();
  }

  // Payment method management
  Future<void> loadPaymentMethods() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (!_userController.isLoggedIn) {
        _paymentMethods.clear();
        return;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, this would fetch user's payment methods from API
      // For now, we'll use the demo data
    } catch (e) {
      _errorMessage.value = 'Failed to load payment methods: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> addPaymentMethod(PaymentMethod paymentMethod) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (!_userController.isLoggedIn) {
        _errorMessage.value = 'User not logged in';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // If this is the first payment method, make it default
      final isFirstMethod = _paymentMethods.isEmpty;
      final methodToAdd = isFirstMethod ? paymentMethod.copyWith(isDefault: true) : paymentMethod;

      _paymentMethods.add(methodToAdd);
      await _savePaymentMethodsToStorage();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to add payment method: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updatePaymentMethod(PaymentMethod updatedMethod) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _paymentMethods.indexWhere((method) => method.id == updatedMethod.id);
      if (index == -1) {
        _errorMessage.value = 'Payment method not found';
        return false;
      }

      _paymentMethods[index] = updatedMethod;

      // Update selected method if it's the same
      if (_selectedPaymentMethod.value?.id == updatedMethod.id) {
        _selectedPaymentMethod.value = updatedMethod;
      }

      await _savePaymentMethodsToStorage();
      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update payment method: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deletePaymentMethod(String methodId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final method = _paymentMethods.firstWhereOrNull((m) => m.id == methodId);
      if (method == null) {
        _errorMessage.value = 'Payment method not found';
        return false;
      }

      // Don't allow deletion of the last payment method
      if (_paymentMethods.length == 1) {
        _errorMessage.value = 'Cannot delete the last payment method';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _paymentMethods.removeWhere((m) => m.id == methodId);

      // If deleted method was default, make another one default
      if (method.isDefault && _paymentMethods.isNotEmpty) {
        final newDefault = _paymentMethods.first.copyWith(isDefault: true);
        final index = _paymentMethods.indexWhere((m) => m.id == newDefault.id);
        _paymentMethods[index] = newDefault;
      }

      // Clear selected method if it was deleted
      if (_selectedPaymentMethod.value?.id == methodId) {
        _selectedPaymentMethod.value = null;
      }

      await _savePaymentMethodsToStorage();
      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to delete payment method: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> setDefaultPaymentMethod(String methodId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 300));

      // Remove default from all methods
      for (int i = 0; i < _paymentMethods.length; i++) {
        _paymentMethods[i] = _paymentMethods[i].copyWith(isDefault: false);
      }

      // Set new default
      final index = _paymentMethods.indexWhere((method) => method.id == methodId);
      if (index == -1) {
        _errorMessage.value = 'Payment method not found';
        return false;
      }

      _paymentMethods[index] = _paymentMethods[index].copyWith(isDefault: true);
      await _savePaymentMethodsToStorage();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to set default payment method: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Payment processing
  Future<PaymentResult?> processPayment({
    required String paymentMethodId,
    required double amount,
    required String orderId,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final paymentMethod = await getPaymentMethodById(paymentMethodId);
      if (paymentMethod == null) {
        _errorMessage.value = 'Payment method not found';
        return PaymentResult.failure('Payment method not found');
      }

      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, randomly succeed or fail
      final isSuccess = DateTime.now().millisecond % 10 != 0; // 90% success rate

      PaymentResult result;
      if (isSuccess) {
        result = PaymentResult.success('txn_${DateTime.now().millisecondsSinceEpoch}');
      } else {
        result = PaymentResult.failure('Payment declined by bank');
      }

      _paymentHistory.add(result);
      return result;
    } catch (e) {
      _errorMessage.value = 'Payment processing failed: ${e.toString()}';
      return PaymentResult.failure('Payment processing failed');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<PaymentResult?> processRefund({
    required String paymentResultId,
    required double amount,
    required String reason,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate refund processing
      await Future.delayed(const Duration(seconds: 1));

      final refundResult = PaymentResult.success('refund_${DateTime.now().millisecondsSinceEpoch}');
      _paymentHistory.add(refundResult);

      return refundResult;
    } catch (e) {
      _errorMessage.value = 'Refund processing failed: ${e.toString()}';
      return PaymentResult.failure('Refund processing failed');
    } finally {
      _isLoading.value = false;
    }
  }

  // Payment method selection
  void selectPaymentMethod(PaymentMethod method) {
    _selectedPaymentMethod.value = method;
  }

  void clearSelectedPaymentMethod() {
    _selectedPaymentMethod.value = null;
  }

  // Payment method retrieval
  Future<PaymentMethod?> getPaymentMethodById(String methodId) async {
    return _paymentMethods.firstWhereOrNull((method) => method.id == methodId);
  }

  List<PaymentMethod> getPaymentMethodsByType(PaymentType type) {
    return _paymentMethods.where((method) => method.type == type).toList();
  }

  // Validation
  bool validatePaymentMethod(PaymentMethod method) {
    switch (method.type) {
      case PaymentType.creditCard:
      case PaymentType.debitCard:
        return method.cardNumber != null && method.expiryDate != null && method.cardHolderName != null;
      case PaymentType.paypal:
      case PaymentType.applePay:
      case PaymentType.googlePay:
      case PaymentType.cash:
        return true;
    }
  }

  bool isPaymentMethodExpired(PaymentMethod method) {
    if (method.expiryDate == null) return false;

    try {
      final parts = method.expiryDate!.split('/');
      if (parts.length != 2) return false;

      final month = int.parse(parts[0]);
      final year = int.parse('20${parts[1]}');
      final expiryDate = DateTime(year, month + 1, 0); // Last day of expiry month

      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      return true; // Consider invalid format as expired
    }
  }

  // Private methods
  void _setupUserListener() {
    // Clear payment methods when user logs out
    ever(_userController.currentUser.obs, (user) {
      if (user == null) {
        _paymentMethods.clear();
        _selectedPaymentMethod.value = null;
        _paymentHistory.clear();
      } else {
        loadPaymentMethods();
      }
    });
  }

  Future<void> _savePaymentMethodsToStorage() async {
    try {
      if (!_userController.isLoggedIn) return;

      final prefs = await SharedPreferences.getInstance();
      final methodsData = _paymentMethods.map((method) => method.toJson()).toList();
      await prefs.setString('payment_methods_${_userController.currentUser!.id}', jsonEncode(methodsData));
    } catch (e) {
      // In production, use a proper logging framework
      _errorMessage.value = 'Error saving payment methods: ${e.toString()}';
    }
  }

  Future<void> _loadPaymentMethodsFromStorage() async {
    try {
      if (!_userController.isLoggedIn) return;

      final prefs = await SharedPreferences.getInstance();
      final methodsDataString = prefs.getString('payment_methods_${_userController.currentUser!.id}');

      if (methodsDataString != null) {
        final methodsData = jsonDecode(methodsDataString) as List;
        _paymentMethods.clear();
        _paymentMethods.addAll(
          methodsData.map((methodJson) => PaymentMethod.fromJson(methodJson)).toList(),
        );
      }
    } catch (e) {
      // In production, use a proper logging framework
      _errorMessage.value = 'Error loading payment methods: ${e.toString()}';
    }
  }

  Future<void> _loadDemoPaymentMethods() async {
    try {
      // Load demo payment methods from JSON file
      if (_paymentMethods.isEmpty && _userController.isLoggedIn) {
        await DemoDataService.instance.loadDemoData();
        final paymentMethods = DemoDataService.instance.getPaymentMethods();
        _paymentMethods.addAll(paymentMethods);
      }
    } catch (e) {
      // Fallback to hardcoded demo payment methods if JSON loading fails
      if (_paymentMethods.isEmpty && _userController.isLoggedIn) {
        _paymentMethods.addAll([
          PaymentMethod(
            id: 'pm_001',
            type: PaymentType.creditCard,
            displayName: 'Visa ending in 4242',
            cardNumber: '4242',
            expiryDate: '12/25',
            cardHolderName: 'John Doe',
            isDefault: true,
          ),
          PaymentMethod(
            id: 'pm_002',
            type: PaymentType.paypal,
            displayName: 'PayPal Account',
            isDefault: false,
          ),
          PaymentMethod(
            id: 'pm_003',
            type: PaymentType.applePay,
            displayName: 'Apple Pay',
            isDefault: false,
          ),
        ]);
      }
    }
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  String formatCardNumber(String cardNumber) {
    // Format card number with spaces (e.g., "1234 5678 9012 3456")
    final cleaned = cardNumber.replaceAll(RegExp(r'\D'), '');
    final buffer = StringBuffer();

    for (int i = 0; i < cleaned.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(cleaned[i]);
    }

    return buffer.toString();
  }

  String maskCardNumber(String cardNumber) {
    if (cardNumber.length < 4) return cardNumber;
    final lastFour = cardNumber.substring(cardNumber.length - 4);
    return '**** **** **** $lastFour';
  }

  bool isValidCardNumber(String cardNumber) {
    final cleaned = cardNumber.replaceAll(RegExp(r'\D'), '');
    return cleaned.length >= 13 && cleaned.length <= 19;
  }

  bool isValidExpiryDate(String expiryDate) {
    final regex = RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$');
    if (!regex.hasMatch(expiryDate)) return false;

    try {
      final parts = expiryDate.split('/');
      final month = int.parse(parts[0]);
      final year = int.parse('20${parts[1]}');
      final expiry = DateTime(year, month + 1, 0);

      return DateTime.now().isBefore(expiry);
    } catch (e) {
      return false;
    }
  }

  PaymentType? getCardTypeFromNumber(String cardNumber) {
    final cleaned = cardNumber.replaceAll(RegExp(r'\D'), '');

    if (cleaned.startsWith('4')) {
      return PaymentType.creditCard; // Visa
    } else if (cleaned.startsWith(RegExp(r'^5[1-5]'))) {
      return PaymentType.creditCard; // Mastercard
    } else if (cleaned.startsWith(RegExp(r'^3[47]'))) {
      return PaymentType.creditCard; // American Express
    }

    return PaymentType.creditCard; // Default to credit card
  }
}
