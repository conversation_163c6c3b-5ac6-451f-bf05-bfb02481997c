/// Application-wide constants and configuration values
class AppConstants {
  AppConstants._();

  // App Information
  static const String appName = 'Restaurant Hub';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'A comprehensive restaurant management and food delivery platform';

  // Business Logic Constants
  static const double taxRate = 0.08; // 8% tax rate
  static const double baseDeliveryFee = 3.99;
  static const double freeDeliveryThreshold = 35.0;
  static const double minimumOrderAmount = 10.0;
  static const double maximumDeliveryDistance = 25.0; // in kilometers

  // Time Constants
  static const int defaultEstimatedDeliveryMinutes = 45;
  static const int maxDeliveryTimeMinutes = 120;
  static const int orderCancellationTimeMinutes = 15;
  static const int sessionTimeoutMinutes = 30;

  // Pagination and Limits
  static const int defaultPageSize = 20;
  static const int maxSearchResults = 100;
  static const int maxCartItems = 50;
  static const int maxOrderHistory = 500;

  // Validation Constants
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 50;
  static const int maxCommentLength = 500;
  static const int maxSpecialInstructionsLength = 200;

  // Rating Constants
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const int minRatingCommentLength = 10;

  // File Upload Constants
  static const int maxImageSizeMB = 5;
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const int maxProfileImageSizeMB = 2;

  // API Constants
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  static const int retryDelaySeconds = 2;

  // Cache Constants
  static const int cacheExpirationHours = 24;
  static const int imageCacheExpirationDays = 7;

  // Notification Constants
  static const String defaultNotificationChannel = 'restaurant_hub_general';
  static const String orderNotificationChannel = 'restaurant_hub_orders';
  static const String promotionNotificationChannel = 'restaurant_hub_promotions';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String cartDataKey = 'cart_data';
  static const String paymentMethodsKey = 'payment_methods';
  static const String driverStatusKey = 'driver_status';
  static const String themePreferenceKey = 'theme_preference';
  static const String languagePreferenceKey = 'language_preference';

  // Demo Data Constants
  static const String demoDataAssetPath = 'demo_data.json';
  static const String demoAdminEmail = '<EMAIL>';
  static const String demoCustomerEmail = '<EMAIL>';
  static const String demoRestaurantEmail = '<EMAIL>';
  static const String demoDriverEmail = '<EMAIL>';
  static const String demoPassword = 'password123';

  // Error Messages
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error. Please try again later.';
  static const String authenticationErrorMessage = 'Authentication failed. Please log in again.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  static const String permissionErrorMessage = 'You do not have permission to perform this action.';

  // Success Messages
  static const String loginSuccessMessage = 'Welcome back!';
  static const String registrationSuccessMessage = 'Account created successfully!';
  static const String orderPlacedSuccessMessage = 'Your order has been placed successfully!';
  static const String profileUpdatedSuccessMessage = 'Profile updated successfully!';
  static const String passwordChangedSuccessMessage = 'Password changed successfully!';

  // Feature Flags
  static const bool enablePushNotifications = true;
  static const bool enableLocationTracking = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableDarkMode = true;
  static const bool enableMultiLanguage = false;
  static const bool enableSocialLogin = false;
  static const bool enablePaymentGateway = false; // Set to true when payment gateway is integrated
  static const bool enableRealTimeUpdates = true;

  // Development Constants
  static const bool isDebugMode = true; // Should be false in production
  static const bool enableDemoData = true;
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = true;

  // URL Constants (for future API integration)
  static const String baseApiUrl = 'https://api.restauranthub.com/v1';
  static const String authEndpoint = '/auth';
  static const String usersEndpoint = '/users';
  static const String restaurantsEndpoint = '/restaurants';
  static const String ordersEndpoint = '/orders';
  static const String paymentsEndpoint = '/payments';
  static const String menuEndpoint = '/menu';

  // Social Media URLs
  static const String facebookUrl = 'https://facebook.com/restauranthub';
  static const String twitterUrl = 'https://twitter.com/restauranthub';
  static const String instagramUrl = 'https://instagram.com/restauranthub';
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = 'https://restauranthub.com/privacy';
  static const String termsOfServiceUrl = 'https://restauranthub.com/terms';

  // Animation Constants
  static const int defaultAnimationDurationMs = 300;
  static const int fastAnimationDurationMs = 150;
  static const int slowAnimationDurationMs = 500;

  // UI Constants
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultElevation = 2.0;
  static const double highElevation = 8.0;

  // Map Constants
  static const double defaultMapZoom = 15.0;
  static const double maxMapZoom = 20.0;
  static const double minMapZoom = 10.0;
  static const int locationUpdateIntervalSeconds = 30;
}

/// Environment-specific configuration
class EnvironmentConfig {
  EnvironmentConfig._();

  static const String environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');

  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';

  static String get apiBaseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.restauranthub.com/v1';
      case 'staging':
        return 'https://staging-api.restauranthub.com/v1';
      default:
        return 'https://dev-api.restauranthub.com/v1';
    }
  }

  static bool get enableLogging => !isProduction;
  static bool get enableDemoData => !isProduction;
  static bool get enableLocationTracking => true;
  static bool get enablePerformanceMonitoring => !isProduction;
}
