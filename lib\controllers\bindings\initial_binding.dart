import 'package:get/get.dart';
import '../user_controller.dart';
import '../restaurant_controller.dart';
import '../cart_controller.dart';
import '../order_controller.dart';
import '../payment_controller.dart';
import '../admin_controller.dart';
import '../driver_controller.dart';
import '../../services/audit_service.dart';
import '../../services/error_service.dart';
import '../../services/system_monitoring_service.dart';
import '../../services/authentication_service.dart';
import '../../services/realtime_service.dart';
import '../../services/notification_service.dart';
import '../../services/api_service.dart';
import '../../services/location_service.dart';
import '../../services/cache_service.dart';
import '../../services/logging_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize services first (they are dependencies for controllers)
    Get.lazyPut<ErrorService>(() => ErrorService(), fenix: true);
    Get.lazyPut<AuditService>(() => AuditService(), fenix: true);
    Get.lazyPut<SystemMonitoringService>(() => SystemMonitoringService(), fenix: true);
    Get.lazyPut<AuthenticationService>(() => AuthenticationService(), fenix: true);
    Get.lazyPut<RealtimeService>(() => RealtimeService(), fenix: true);
    Get.lazyPut<NotificationService>(() => NotificationService(), fenix: true);
    Get.lazyPut<ApiService>(() => ApiService(), fenix: true);
    Get.lazyPut<LocationService>(() => LocationService(), fenix: true);
    Get.lazyPut<CacheService>(() => CacheService(), fenix: true);
    Get.lazyPut<PerformanceService>(() => PerformanceService(), fenix: true);
    Get.lazyPut<LoggingService>(() => LoggingService(), fenix: true);

    // Initialize all controllers as lazy singletons
    Get.lazyPut<UserController>(() => UserController(), fenix: true);
    Get.lazyPut<RestaurantController>(() => RestaurantController(), fenix: true);
    Get.lazyPut<CartController>(() => CartController(), fenix: true);
    Get.lazyPut<OrderController>(() => OrderController(), fenix: true);
    Get.lazyPut<PaymentController>(() => PaymentController(), fenix: true);
    Get.lazyPut<AdminController>(() => AdminController(), fenix: true);
    Get.lazyPut<DriverController>(() => DriverController(), fenix: true);
  }
}
