import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/demo_data_service.dart';
import '../services/authentication_service.dart';
import '../core/app_routes.dart';
import '../core/validators.dart';
import '../views/shared/snackbars.dart';

class UserController extends GetxController {
  // Observable variables
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<UserModel> _allUsers = <UserModel>[].obs;
  final RxString _currentSessionId = ''.obs;

  // Services
  AuthenticationService? _authService;

  // Getters
  UserModel? get currentUser => _currentUser.value;
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get errorMessage => _errorMessage.value;
  List<UserModel> get allUsers => _allUsers;
  bool get isAdmin => currentUser?.role.isAdmin ?? false;
  bool get isCustomer => currentUser?.role.isCustomer ?? false;
  bool get isRestaurant => currentUser?.role == UserRole.restaurant;
  bool get isDriver => currentUser?.role == UserRole.driver;

  @override
  void onInit() {
    super.onInit();
    _loadUserFromStorage();
    _loadDemoUsers();
    // Initialize auth service and session validation after a delay to avoid circular dependency
    Future.delayed(const Duration(milliseconds: 100), () {
      _authService = Get.find<AuthenticationService>();
      _startSessionValidation();
    });
  }

  void _startSessionValidation() {
    // Check session validity every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_isLoggedIn.value && _currentSessionId.value.isNotEmpty) {
        _validateCurrentSession();
      }
    });
  }

  Future<void> _validateCurrentSession() async {
    if (_currentSessionId.value.isEmpty || _authService == null) return;

    final isValid = await _authService!.isSessionValid(_currentSessionId.value);
    if (!isValid) {
      // Session expired, logout user
      _errorMessage.value = 'Your session has expired. Please login again.';
      await _handleSessionExpiry();
    }
  }

  Future<void> _handleSessionExpiry() async {
    await logout();
    Get.offAllNamed(AppRoutes.login);
    ErrorSnackBar.show('Session expired. Please login again.');
  }

  // Authentication methods
  Future<bool> login(String email, String password) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate input using proper validators
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        _errorMessage.value = emailError;
        return false;
      }

      final passwordError = Validators.validatePassword(password, requireComplexity: false);
      if (passwordError != null) {
        _errorMessage.value = passwordError;
        return false;
      }

      // For demo purposes, find user in demo data and simulate authentication
      final user = _allUsers.firstWhereOrNull(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );

      if (user == null) {
        _errorMessage.value = 'Invalid email or password';
        return false;
      }

      // Use AuthenticationService for secure login (demo mode)
      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final result = await _authService!.authenticateUser(
        email,
        password,
        deviceInfo: 'Flutter App - ${DateTime.now().toString()}',
      );

      if (!result.success) {
        _errorMessage.value = result.error ?? 'Login failed';
        return false;
      }

      // Set current user and session
      await _setCurrentUser(user);
      _currentSessionId.value = result.sessionId ?? '';
      _isLoggedIn.value = true;

      // Navigate based on user role
      _navigateBasedOnRole();

      return true;
    } catch (e) {
      _errorMessage.value = 'Login failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    String? phone,
    UserRole role = UserRole.customer,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate input using proper validators
      final firstNameError = Validators.validateName(firstName, fieldName: 'First name');
      if (firstNameError != null) {
        _errorMessage.value = firstNameError;
        return false;
      }

      final lastNameError = Validators.validateName(lastName, fieldName: 'Last name');
      if (lastNameError != null) {
        _errorMessage.value = lastNameError;
        return false;
      }

      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        _errorMessage.value = emailError;
        return false;
      }

      final passwordError = Validators.validatePassword(password, requireComplexity: true);
      if (passwordError != null) {
        _errorMessage.value = passwordError;
        return false;
      }

      if (phone != null) {
        final phoneError = Validators.validatePhone(phone);
        if (phoneError != null) {
          _errorMessage.value = phoneError;
          return false;
        }
      }

      // Use AuthenticationService for secure registration
      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final success = await _authService!.registerUser(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        phone: phone,
        role: role,
      );

      if (!success) {
        _errorMessage.value = 'Registration failed. Email may already be in use.';
        return false;
      }

      // Check if user already exists in demo data
      final existingUser = _allUsers.firstWhereOrNull(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );

      if (existingUser != null) {
        _errorMessage.value = 'User with this email already exists';
        return false;
      }

      // Create new user
      final newUser = UserModel(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        dateJoined: DateTime.now(),
        role: role,
        preferences: UserPreferences(),
        stats: UserStats(
          rate: RateModel(
            id: 'rate_${DateTime.now().millisecondsSinceEpoch}',
            userId: 'user_${DateTime.now().millisecondsSinceEpoch}',
            restaurantId: '',
            rating: 0.0,
            comment: '',
            createdAt: DateTime.now(),
          ),
          address: AddressModel(
            id: 'addr_${DateTime.now().millisecondsSinceEpoch}',
            type: 'Home',
            fullAddress: '',
            unit: null,
            isDefault: true,
            estimatedDelivery: '30-45 mins',
          ),
        ),
      );

      _allUsers.add(newUser);
      await _setCurrentUser(newUser);
      _isLoggedIn.value = true;

      // Navigate based on user role
      _navigateBasedOnRole();

      return true;
    } catch (e) {
      _errorMessage.value = 'Registration failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> logout() async {
    try {
      _isLoading.value = true;

      // Use AuthenticationService for secure logout
      if (_currentSessionId.value.isNotEmpty && _authService != null) {
        await _authService!.logout(_currentSessionId.value);
      }

      // Clear user data
      _currentUser.value = null;
      _isLoggedIn.value = false;
      _currentSessionId.value = '';

      // Clear from storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');

      // Navigate to login
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      _errorMessage.value = 'Logout failed: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate email
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        _errorMessage.value = emailError;
        return false;
      }

      // Use AuthenticationService for secure password reset
      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final success = await _authService!.sendPasswordReset(email);

      if (!success) {
        _errorMessage.value = 'No account found with this email address';
        return false;
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Password reset failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> resetPassword(String email, String code, String newPassword) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate inputs
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        _errorMessage.value = emailError;
        return false;
      }

      final passwordError = Validators.validatePassword(newPassword, requireComplexity: true);
      if (passwordError != null) {
        _errorMessage.value = passwordError;
        return false;
      }

      if (code.isEmpty || code.length != 6) {
        _errorMessage.value = 'Please enter a valid 6-digit verification code';
        return false;
      }

      // Use AuthenticationService for secure password reset
      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final success = await _authService!.resetPassword(email, code, newPassword);

      if (!success) {
        _errorMessage.value = 'Invalid or expired verification code';
        return false;
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Password reset failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> sendEmailVerification(String email) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final success = await _authService!.sendEmailVerification(email);

      if (!success) {
        _errorMessage.value = 'Failed to send verification email';
        return false;
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Email verification failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> verifyEmail(String email, String code) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (code.isEmpty || code.length != 6) {
        _errorMessage.value = 'Please enter a valid 6-digit verification code';
        return false;
      }

      if (_authService == null) {
        _errorMessage.value = 'Authentication service not available';
        return false;
      }

      final success = await _authService!.verifyEmail(email, code);

      if (!success) {
        _errorMessage.value = 'Invalid or expired verification code';
        return false;
      }

      // Update current user if it's their email being verified
      if (_currentUser.value?.email.toLowerCase() == email.toLowerCase()) {
        final updatedUser = _currentUser.value!.copyWith(isEmailVerified: true);
        await _setCurrentUser(updatedUser);
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Email verification failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Profile management
  Future<bool> updateProfile(UserModel updatedUser) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Update in local list
      final index = _allUsers.indexWhere((user) => user.id == updatedUser.id);
      if (index != -1) {
        _allUsers[index] = updatedUser;
      }

      // Update current user if it's the same user
      if (_currentUser.value?.id == updatedUser.id) {
        await _setCurrentUser(updatedUser);
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Profile update failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser.value == null) return false;

    final updatedUser = _currentUser.value!.copyWith(preferences: preferences);
    return await updateProfile(updatedUser);
  }

  Future<bool> updateStats(UserStats stats) async {
    if (_currentUser.value == null) return false;

    final updatedUser = _currentUser.value!.copyWith(stats: stats);
    return await updateProfile(updatedUser);
  }

  Future<bool> updateProfileImage(String imageUrl) async {
    if (_currentUser.value == null) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call for image upload
      await Future.delayed(const Duration(milliseconds: 800));

      final updatedUser = _currentUser.value!.copyWith(
        profileImageUrl: imageUrl.isEmpty ? null : imageUrl,
      );

      return await updateProfile(updatedUser);
    } catch (e) {
      _errorMessage.value = 'Failed to update profile image: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // User management (for admin)
  Future<List<UserModel>> getAllUsers() async {
    try {
      _isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      return _allUsers;
    } catch (e) {
      _errorMessage.value = 'Failed to load users: ${e.toString()}';
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  Future<List<UserModel>> getUsersByRole(UserRole role) async {
    final allUsers = await getAllUsers();
    return allUsers.where((user) => user.role == role).toList();
  }

  Future<bool> deleteUser(String userId) async {
    if (!isAdmin) {
      _errorMessage.value = 'Unauthorized: Admin access required';
      return false;
    }

    try {
      _isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _allUsers.removeWhere((user) => user.id == userId);
      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to delete user: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Private methods
  Future<void> _setCurrentUser(UserModel user) async {
    _currentUser.value = user;

    // Save to storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user', jsonEncode(user.toJson()));
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');

      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _currentUser.value = UserModel.fromJson(userData);
        _isLoggedIn.value = true;
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'Failed to load user: ${e.toString()}',
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _navigateBasedOnRole() {
    switch (currentUser?.role) {
      case UserRole.admin:
        Get.offAllNamed(AppRoutes.admin);
        break;
      case UserRole.restaurant:
        Get.offAllNamed(AppRoutes.restaurant);
        break;
      case UserRole.driver:
        Get.offAllNamed(AppRoutes.driver);
        break;
      case UserRole.customer:
      default:
        Get.offAllNamed(AppRoutes.home);
        break;
    }
  }

  Future<void> _loadDemoUsers() async {
    try {
      // Load demo data from JSON file
      await DemoDataService.instance.loadDemoData();
      final users = DemoDataService.instance.getUsers();
      _allUsers.addAll(users);
    } catch (e) {
      // Fallback to hardcoded demo users if JSON loading fails
      _allUsers.addAll([
        UserModel(
          id: 'demo_admin',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          dateJoined: DateTime.now().subtract(const Duration(days: 30)),
          role: UserRole.admin,
          isEmailVerified: true,
          preferences: UserPreferences(),
          stats: UserStats(
            rate: RateModel(
              id: 'rate_admin',
              userId: 'demo_admin',
              restaurantId: '',
              rating: 0.0,
              comment: '',
              createdAt: DateTime.now(),
            ),
            address: AddressModel(
              id: 'addr_admin',
              type: 'Office',
              fullAddress: '123 Admin Street, City',
              unit: null,
              isDefault: true,
              estimatedDelivery: '15-25 mins',
            ),
          ),
        ),
        UserModel(
          id: 'demo_customer',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          dateJoined: DateTime.now().subtract(const Duration(days: 15)),
          role: UserRole.customer,
          isEmailVerified: true,
          preferences: UserPreferences(),
          stats: UserStats(
            totalOrders: 5,
            totalSpent: 125.50,
            rate: RateModel(
              id: 'rate_customer',
              userId: 'demo_customer',
              restaurantId: '',
              rating: 4.5,
              comment: 'Great service!',
              createdAt: DateTime.now(),
            ),
            address: AddressModel(
              id: 'addr_customer',
              type: 'Home',
              fullAddress: '456 Customer Ave, City',
              unit: null,
              isDefault: true,
              estimatedDelivery: '25-35 mins',
            ),
          ),
        ),
      ]);
    }
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  bool hasPermission(String permission) {
    // Define permissions based on user role
    switch (currentUser?.role) {
      case UserRole.admin:
        return true; // Admin has all permissions
      case UserRole.restaurant:
        return ['manage_menu', 'view_orders', 'update_order_status'].contains(permission);
      case UserRole.driver:
        return ['view_deliveries', 'update_delivery_status'].contains(permission);
      case UserRole.customer:
        return ['place_order', 'view_own_orders', 'rate_restaurant'].contains(permission);
      default:
        return false;
    }
  }
}
