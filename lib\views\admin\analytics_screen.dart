import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../services/audit_service.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = ['Overview', 'Revenue', 'Users', 'Orders'];
  final AdminController _adminController = Get.find<AdminController>();
  final AuditService _auditService = Get.find<AuditService>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Analytics',
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildRevenueTab(),
          _buildUsersTab(),
          _buildOrdersTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Obx(() {
      if (_adminController.isLoading) {
        return const LoadingWidget();
      }

      final stats = _adminController.adminStats;
      final auditSummary = _auditService.getLogsSummary();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Key Metrics
            Text(
              'Key Metrics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Total Revenue',
                    value: '\$${stats.totalRevenue.toStringAsFixed(1)}K',
                    icon: Icons.attach_money,
                    color: Colors.green,
                    subtitle: 'Last 30 days',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: StatCard(
                    title: 'Total Orders',
                    value: '${stats.totalOrders}',
                    icon: Icons.shopping_cart,
                    color: Colors.blue,
                    subtitle: 'All time',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Active Users',
                    value: '${stats.activeUsers}',
                    icon: Icons.people,
                    color: Colors.purple,
                    subtitle: 'Registered users',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: StatCard(
                    title: 'Active Restaurants',
                    value: '${stats.activeRestaurants}',
                    icon: Icons.restaurant,
                    color: Colors.orange,
                    subtitle: 'Approved partners',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Audit Summary
            Text(
              'System Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Total Audit Logs',
                    value: '${auditSummary['total'] ?? 0}',
                    icon: Icons.history,
                    color: Colors.indigo,
                    subtitle: 'All activities',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: StatCard(
                    title: 'Critical Events',
                    value: '${auditSummary['critical'] ?? 0}',
                    icon: Icons.warning,
                    color: Colors.red,
                    subtitle: 'Requires attention',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: 'Pending Orders',
                    value: '${stats.pendingOrders}',
                    icon: Icons.pending,
                    color: Colors.orange,
                    subtitle: 'Awaiting processing',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: StatCard(
                    title: 'Completed Orders',
                    value: '${stats.completedOrders}',
                    icon: Icons.check_circle,
                    color: Colors.green,
                    subtitle: 'Successfully delivered',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Top Performing Restaurants
            Text(
              'Top Performing Restaurants',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            ..._getTopRestaurants().map((restaurant) => _buildRestaurantCard(restaurant)),
          ],
        ),
      );
    });
  }

  Widget _buildRevenueTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Summary
          Text(
            'Revenue Summary',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildRevenueItem('Today', '\$2,450', '+12%'),
                  _buildDivider(),
                  _buildRevenueItem('This Week', '\$18,750', '+8%'),
                  _buildDivider(),
                  _buildRevenueItem('This Month', '\$125,400', '+15%'),
                  _buildDivider(),
                  _buildRevenueItem('This Year', '\$1,254,000', '+22%'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Revenue by Category
          Text(
            'Revenue by Category',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: SizedBox(
              height: 200,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.pie_chart,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Pie chart visualization would go here',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Growth
          Text(
            'User Growth',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'New Users',
                  value: '234',
                  icon: Icons.person_add,
                  color: Colors.green,
                  subtitle: 'This week',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Active Users',
                  value: '2,341',
                  icon: Icons.people,
                  color: Colors.blue,
                  subtitle: 'Last 30 days',
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // User Distribution
          Text(
            'User Distribution',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildUserDistributionItem('Customers', '1,856', '79%', Colors.blue),
                  _buildDivider(),
                  _buildUserDistributionItem('Restaurants', '156', '7%', Colors.green),
                  _buildDivider(),
                  _buildUserDistributionItem('Drivers', '289', '12%', Colors.orange),
                  _buildDivider(),
                  _buildUserDistributionItem('Admins', '40', '2%', Colors.purple),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Statistics
          Text(
            'Order Statistics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Total Orders',
                  value: '8,547',
                  icon: Icons.shopping_cart,
                  color: Colors.blue,
                  subtitle: 'All time',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Avg Delivery Time',
                  value: '28 min',
                  icon: Icons.timer,
                  color: Colors.orange,
                  subtitle: 'Last 30 days',
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Order Status Distribution
          Text(
            'Order Status Distribution',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildOrderStatusItem('Completed', '7,892', '92%', Colors.green),
                  _buildDivider(),
                  _buildOrderStatusItem('In Progress', '89', '1%', Colors.blue),
                  _buildDivider(),
                  _buildOrderStatusItem('Cancelled', '566', '7%', Colors.red),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRestaurantCard(Map<String, dynamic> restaurant) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.restaurant,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  restaurant['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${restaurant['orders']} orders • ${restaurant['rating']} ⭐',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            restaurant['revenue'],
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueItem(String period, String amount, String change) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            period,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Text(
                amount,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                change,
                style: const TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserDistributionItem(String type, String count, String percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              type,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            count,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Text(
            percentage,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusItem(String status, String count, String percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              status,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            count,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Text(
            percentage,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: Colors.grey[200],
    );
  }

  List<Map<String, dynamic>> _getTopRestaurants() {
    return [
      {
        'name': 'McDonald\'s',
        'orders': 245,
        'rating': '4.5',
        'revenue': '\$12,450',
      },
      {
        'name': 'Pizza Hut',
        'orders': 189,
        'rating': '4.3',
        'revenue': '\$9,870',
      },
      {
        'name': 'Subway',
        'orders': 156,
        'rating': '4.2',
        'revenue': '\$7,650',
      },
    ];
  }
}
