import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/restaurant_controller.dart';
import '../../models/menu_item.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';
import '../shared/snackbars.dart';

class MenuManagementScreen extends StatefulWidget {
  const MenuManagementScreen({super.key});

  @override
  State<MenuManagementScreen> createState() => _MenuManagementScreenState();
}

class _MenuManagementScreenState extends State<MenuManagementScreen> {
  final UserController _userController = Get.find<UserController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();

  @override
  void initState() {
    super.initState();
    _loadMenuItems();
  }

  Future<void> _loadMenuItems() async {
    final restaurant = _userController.currentUser;
    if (restaurant != null) {
      await _restaurantController.loadMenuItems(restaurant.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Menu Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddItemDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Category Filter
          _buildCategoryFilter(),
          // Menu Items List
          Expanded(
            child: _buildMenuItemsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddItemDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      color: Colors.white,
      child: Obx(() {
        final categories = _restaurantController.categories;
        final selectedCategory = _restaurantController.selectedCategory;

        return SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: categories.length + 1, // +1 for "Add Category"
            itemBuilder: (context, index) {
              if (index == categories.length) {
                return Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: ActionChip(
                    label: const Text('+ Add Category'),
                    onPressed: _showAddCategoryDialog,
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                  ),
                );
              }

              final category = categories[index];
              final isSelected = category == selectedCategory;

              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    _restaurantController.filterByCategory(category);
                  },
                  selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).primaryColor,
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildMenuItemsList() {
    return Obx(() {
      if (_restaurantController.isLoading) {
        return const LoadingWidget(message: 'Loading menu items...');
      }

      if (_restaurantController.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: _restaurantController.errorMessage,
          onRetry: _loadMenuItems,
        );
      }

      final menuItems = _restaurantController.filteredMenuItems;
      if (menuItems.isEmpty) {
        return const EmptyStateWidget(
          title: 'No Menu Items',
          message: 'Start by adding your first menu item.',
          icon: Icons.restaurant_menu,
          actionText: 'Add Item',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: menuItems.length,
        itemBuilder: (context, index) {
          final menuItem = menuItems[index];
          return _buildMenuItemCard(menuItem);
        },
      );
    });
  }

  Widget _buildMenuItemCard(MenuItem menuItem) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          Row(
            children: [
              // Menu Item Image
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.fastfood,
                  size: 40,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 16),
              // Menu Item Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            menuItem.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Switch(
                          value: menuItem.isAvailable,
                          onChanged: (value) => _toggleAvailability(menuItem, value),
                          activeColor: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      menuItem.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${menuItem.price.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                menuItem.category,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue[800],
                                ),
                              ),
                            ),
                            if (menuItem.allergens.isNotEmpty) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.orange[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Allergens',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.orange[800],
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: SecondaryButton(
                  text: 'Edit',
                  onPressed: () => _showEditItemDialog(menuItem),
                  height: 36,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SecondaryButton(
                  text: 'Delete',
                  onPressed: () => _showDeleteConfirmation(menuItem),
                  textColor: Colors.red,
                  borderColor: Colors.red,
                  height: 36,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _toggleAvailability(MenuItem menuItem, bool isAvailable) async {
    final success = await _restaurantController.toggleMenuItemAvailability(menuItem.id);
    if (!mounted) return;

    if (success) {
      SuccessSnackBar.show(
        '${menuItem.name} ${isAvailable ? 'enabled' : 'disabled'}',
      );
    } else {
      ErrorSnackBar.show(
        _restaurantController.errorMessage.isNotEmpty
            ? _restaurantController.errorMessage
            : 'Failed to update item availability',
      );
    }
  }

  void _showAddItemDialog() {
    _showItemDialog();
  }

  void _showEditItemDialog(MenuItem menuItem) {
    _showItemDialog(menuItem: menuItem);
  }

  void _showItemDialog({MenuItem? menuItem}) {
    final isEditing = menuItem != null;
    final nameController = TextEditingController(text: menuItem?.name ?? '');
    final descriptionController = TextEditingController(text: menuItem?.description ?? '');
    final priceController = TextEditingController(text: menuItem?.price.toString() ?? '');
    String selectedCategory = menuItem?.category ?? _restaurantController.categories.first;

    Get.dialog(
      AlertDialog(
        title: Text(isEditing ? 'Edit Menu Item' : 'Add Menu Item'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Item Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: priceController,
                decoration: const InputDecoration(
                  labelText: 'Price',
                  border: OutlineInputBorder(),
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                ),
                items: _restaurantController.categories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedCategory = value!;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Validate input
              if (nameController.text.trim().isEmpty) {
                ErrorSnackBar.show('Please enter a menu item name');
                return;
              }
              if (descriptionController.text.trim().isEmpty) {
                ErrorSnackBar.show('Please enter a description');
                return;
              }
              if (priceController.text.trim().isEmpty) {
                ErrorSnackBar.show('Please enter a price');
                return;
              }

              final price = double.tryParse(priceController.text.trim());
              if (price == null || price <= 0) {
                ErrorSnackBar.show('Please enter a valid price');
                return;
              }

              // Create or update menu item
              final newMenuItem = MenuItem(
                id: isEditing ? menuItem.id : 'menu_${DateTime.now().millisecondsSinceEpoch}',
                name: nameController.text.trim(),
                description: descriptionController.text.trim(),
                price: price,
                image: menuItem?.image ?? 'https://via.placeholder.com/150',
                category: selectedCategory,
                isAvailable: menuItem?.isAvailable ?? true,
                allergens: menuItem?.allergens ?? [],
              );

              bool success;
              if (isEditing) {
                success = await _restaurantController.updateMenuItem(newMenuItem);
              } else {
                success = await _restaurantController.addMenuItem(newMenuItem);
              }

              if (!mounted) return;
              Get.back();

              if (success) {
                SuccessSnackBar.show(
                  isEditing ? 'Menu item updated!' : 'Menu item added!',
                );
              } else {
                ErrorSnackBar.show(
                  _restaurantController.errorMessage.isNotEmpty
                      ? _restaurantController.errorMessage
                      : 'Failed to ${isEditing ? 'update' : 'add'} menu item',
                );
              }
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showAddCategoryDialog() {
    final categoryController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Add Category'),
        content: TextField(
          controller: categoryController,
          decoration: const InputDecoration(
            labelText: 'Category Name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final categoryName = categoryController.text.trim();
              if (categoryName.isEmpty) {
                ErrorSnackBar.show('Please enter a category name');
                return;
              }

              final success = await _restaurantController.addCategory(categoryName);
              if (!mounted) return;
              Get.back();

              if (success) {
                SuccessSnackBar.show('Category added!');
              } else {
                ErrorSnackBar.show(
                  _restaurantController.errorMessage.isNotEmpty
                      ? _restaurantController.errorMessage
                      : 'Failed to add category',
                );
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(MenuItem menuItem) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Menu Item'),
        content: Text('Are you sure you want to delete "${menuItem.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final success = await _restaurantController.deleteMenuItem(menuItem.id);
              if (!mounted) return;
              Get.back();

              if (success) {
                SuccessSnackBar.show('Menu item deleted!');
              } else {
                ErrorSnackBar.show(
                  _restaurantController.errorMessage.isNotEmpty
                      ? _restaurantController.errorMessage
                      : 'Failed to delete menu item',
                );
              }
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
