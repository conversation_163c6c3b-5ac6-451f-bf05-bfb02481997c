import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/restaurant_controller.dart';
import '../../core/app_routes.dart';
import '../../models/user.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';

class RestaurantListScreen extends StatefulWidget {
  const RestaurantListScreen({super.key});

  @override
  State<RestaurantListScreen> createState() => _RestaurantListScreenState();
}

class _RestaurantListScreenState extends State<RestaurantListScreen> {
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _selectedFilter = 'All';

  final List<String> _filterOptions = [
    'All',
    'Fast Food',
    'Pizza',
    'Asian',
    'Italian',
    'Mexican',
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _restaurantController.loadRestaurants();
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _restaurantController.clearSearch();
      }
    });
  }

  void _onSearchChanged(String query) {
    _restaurantController.searchRestaurants(query);
  }

  void _onSearchClear() {
    _searchController.clear();
    _restaurantController.clearSearch();
  }

  void _applyFilter(String filter) {
    _restaurantController.filterRestaurantsByCategory(filter);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SearchAppBar(
        title: 'Restaurants',
        searchHint: 'Search restaurants...',
        searchController: _searchController,
        onSearchChanged: _onSearchChanged,
        onSearchClear: _onSearchClear,
        isSearching: _isSearching,
        onSearchToggle: _toggleSearch,
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: Column(
          children: [
            // Filter Section
            _buildFilterSection(),
            // Restaurant List
            Expanded(
              child: _buildRestaurantList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _filterOptions.map((filter) => _buildFilterChip(filter, _selectedFilter == filter)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = label;
          });
          _applyFilter(label);
        },
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildRestaurantList() {
    return Obx(() {
      if (_restaurantController.isLoading) {
        return const LoadingWidget(message: 'Loading restaurants...');
      }

      if (_restaurantController.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: _restaurantController.errorMessage,
          onRetry: _loadData,
        );
      }

      final restaurants = _restaurantController.restaurants;
      if (restaurants.isEmpty) {
        return const EmptyStateWidget(
          title: 'No Restaurants Found',
          message: 'No restaurants available at the moment.',
          icon: Icons.restaurant,
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: restaurants.length,
        itemBuilder: (context, index) {
          final restaurant = restaurants[index];
          return _buildRestaurantCard(restaurant);
        },
      );
    });
  }

  Widget _buildRestaurantCard(UserModel restaurant) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () {
        _restaurantController.selectRestaurant(restaurant);
        Get.toNamed(AppRoutes.menu);
      },
      child: Row(
        children: [
          // Restaurant Image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.restaurant,
              size: 40,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          // Restaurant Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  restaurant.fullName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  restaurant.stats.address.fullAddress,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    // Rating
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            size: 14,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            restaurant.stats.rate.rating.toStringAsFixed(1),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Delivery Time
                    Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          restaurant.stats.address.estimatedDelivery,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Arrow Icon
          const Icon(
            Icons.chevron_right,
            color: Colors.grey,
          ),
        ],
      ),
    );
  }
}
