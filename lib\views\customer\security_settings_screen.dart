import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../controllers/user_controller.dart';
import '../shared/custom_app_bar.dart';
import '../shared/snackbars.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  final UserController _userController = Get.find<UserController>();
  
  // Security settings
  bool _biometricEnabled = false;
  bool _twoFactorEnabled = false;
  bool _loginNotifications = true;
  bool _deviceManagement = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _biometricEnabled = prefs.getBool('security_biometric') ?? false;
        _twoFactorEnabled = prefs.getBool('security_two_factor') ?? false;
        _loginNotifications = prefs.getBool('security_login_notifications') ?? true;
        _deviceManagement = prefs.getBool('security_device_management') ?? true;
      });
    } catch (e) {
      ErrorSnackBar.show('Failed to load security settings');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('security_biometric', _biometricEnabled);
      await prefs.setBool('security_two_factor', _twoFactorEnabled);
      await prefs.setBool('security_login_notifications', _loginNotifications);
      await prefs.setBool('security_device_management', _deviceManagement);
      
      SuccessSnackBar.show('Security settings saved!');
    } catch (e) {
      ErrorSnackBar.show('Failed to save security settings');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'Privacy & Security',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Password Section
            _buildSectionHeader('Password & Authentication'),
            _buildActionTile(
              icon: Icons.lock_outline,
              title: 'Change Password',
              subtitle: 'Update your account password',
              onTap: _showChangePasswordDialog,
            ),
            _buildSecurityTile(
              icon: Icons.fingerprint,
              title: 'Biometric Login',
              subtitle: 'Use fingerprint or face ID to login',
              value: _biometricEnabled,
              onChanged: (value) {
                setState(() {
                  _biometricEnabled = value;
                });
                _saveSettings();
              },
            ),
            _buildSecurityTile(
              icon: Icons.security,
              title: 'Two-Factor Authentication',
              subtitle: 'Add an extra layer of security',
              value: _twoFactorEnabled,
              onChanged: (value) {
                if (value) {
                  _showTwoFactorSetup();
                } else {
                  _showTwoFactorDisable();
                }
              },
            ),
            
            const SizedBox(height: 24),
            
            // Account Security Section
            _buildSectionHeader('Account Security'),
            _buildSecurityTile(
              icon: Icons.notifications_outlined,
              title: 'Login Notifications',
              subtitle: 'Get notified of new logins',
              value: _loginNotifications,
              onChanged: (value) {
                setState(() {
                  _loginNotifications = value;
                });
                _saveSettings();
              },
            ),
            _buildSecurityTile(
              icon: Icons.devices,
              title: 'Device Management',
              subtitle: 'Manage logged in devices',
              value: _deviceManagement,
              onChanged: (value) {
                setState(() {
                  _deviceManagement = value;
                });
                _saveSettings();
              },
            ),
            _buildActionTile(
              icon: Icons.history,
              title: 'Login History',
              subtitle: 'View recent login activity',
              onTap: _showLoginHistory,
            ),
            
            const SizedBox(height: 24),
            
            // Privacy Section
            _buildSectionHeader('Privacy'),
            _buildActionTile(
              icon: Icons.visibility_off_outlined,
              title: 'Privacy Policy',
              subtitle: 'Read our privacy policy',
              onTap: _showPrivacyPolicy,
            ),
            _buildActionTile(
              icon: Icons.download_outlined,
              title: 'Download My Data',
              subtitle: 'Get a copy of your data',
              onTap: _requestDataDownload,
            ),
            _buildActionTile(
              icon: Icons.delete_outline,
              title: 'Delete Account',
              subtitle: 'Permanently delete your account',
              onTap: _showDeleteAccountDialog,
              isDestructive: true,
            ),
            
            const SizedBox(height: 32),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showLogoutDialog,
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildSecurityTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        secondary: Icon(icon, color: Theme.of(context).primaryColor),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : Theme.of(context).primaryColor,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isDestructive ? Colors.red : null,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    Get.dialog(
      StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Change Password'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: currentPasswordController,
                  obscureText: obscureCurrentPassword,
                  decoration: InputDecoration(
                    labelText: 'Current Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(obscureCurrentPassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () => setState(() => obscureCurrentPassword = !obscureCurrentPassword),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: newPasswordController,
                  obscureText: obscureNewPassword,
                  decoration: InputDecoration(
                    labelText: 'New Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(obscureNewPassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () => setState(() => obscureNewPassword = !obscureNewPassword),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: confirmPasswordController,
                  obscureText: obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'Confirm New Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () => setState(() => obscureConfirmPassword = !obscureConfirmPassword),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (newPasswordController.text != confirmPasswordController.text) {
                  ErrorSnackBar.show('Passwords do not match');
                  return;
                }
                if (newPasswordController.text.length < 6) {
                  ErrorSnackBar.show('Password must be at least 6 characters');
                  return;
                }
                Get.back();
                SuccessSnackBar.show('Password changed successfully!');
              },
              child: const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTwoFactorSetup() {
    InfoSnackBar.show('Two-factor authentication setup coming soon!');
  }

  void _showTwoFactorDisable() {
    Get.dialog(
      AlertDialog(
        title: const Text('Disable Two-Factor Authentication'),
        content: const Text('Are you sure you want to disable two-factor authentication? This will make your account less secure.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _twoFactorEnabled = false;
              });
              _saveSettings();
              Get.back();
              SuccessSnackBar.show('Two-factor authentication disabled');
            },
            child: const Text('Disable'),
          ),
        ],
      ),
    );
  }

  void _showLoginHistory() {
    InfoSnackBar.show('Login history feature coming soon!');
  }

  void _showPrivacyPolicy() {
    InfoSnackBar.show('Privacy policy feature coming soon!');
  }

  void _requestDataDownload() {
    InfoSnackBar.show('Data download feature coming soon!');
  }

  void _showDeleteAccountDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Account'),
        content: const Text('Are you sure you want to permanently delete your account? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              InfoSnackBar.show('Account deletion feature coming soon!');
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _userController.logout();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
