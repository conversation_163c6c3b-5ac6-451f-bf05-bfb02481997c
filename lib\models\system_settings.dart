/// System-wide configuration settings
class SystemSettings {
  final String id;
  final BusinessSettings business;
  final DeliverySettings delivery;
  final PaymentSettings payment;
  final NotificationSettings notification;
  final SecuritySettings security;
  final FeatureFlags features;
  final MaintenanceSettings maintenance;
  final DateTime lastUpdated;
  final String lastUpdatedBy;

  const SystemSettings({
    required this.id,
    required this.business,
    required this.delivery,
    required this.payment,
    required this.notification,
    required this.security,
    required this.features,
    required this.maintenance,
    required this.lastUpdated,
    required this.lastUpdatedBy,
  });

  factory SystemSettings.defaultSettings() => SystemSettings(
        id: 'system_settings_default',
        business: BusinessSettings.defaultSettings(),
        delivery: DeliverySettings.defaultSettings(),
        payment: PaymentSettings.defaultSettings(),
        notification: NotificationSettings.defaultSettings(),
        security: SecuritySettings.defaultSettings(),
        features: FeatureFlags.defaultFlags(),
        maintenance: MaintenanceSettings.defaultSettings(),
        lastUpdated: DateTime.now(),
        lastUpdatedBy: 'system',
      );

  factory SystemSettings.fromJson(Map<String, dynamic> json) => SystemSettings(
        id: json['id'],
        business: BusinessSettings.fromJson(json['business']),
        delivery: DeliverySettings.fromJson(json['delivery']),
        payment: PaymentSettings.fromJson(json['payment']),
        notification: NotificationSettings.fromJson(json['notification']),
        security: SecuritySettings.fromJson(json['security']),
        features: FeatureFlags.fromJson(json['features']),
        maintenance: MaintenanceSettings.fromJson(json['maintenance']),
        lastUpdated: DateTime.parse(json['lastUpdated']),
        lastUpdatedBy: json['lastUpdatedBy'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'business': business.toJson(),
        'delivery': delivery.toJson(),
        'payment': payment.toJson(),
        'notification': notification.toJson(),
        'security': security.toJson(),
        'features': features.toJson(),
        'maintenance': maintenance.toJson(),
        'lastUpdated': lastUpdated.toIso8601String(),
        'lastUpdatedBy': lastUpdatedBy,
      };

  SystemSettings copyWith({
    String? id,
    BusinessSettings? business,
    DeliverySettings? delivery,
    PaymentSettings? payment,
    NotificationSettings? notification,
    SecuritySettings? security,
    FeatureFlags? features,
    MaintenanceSettings? maintenance,
    DateTime? lastUpdated,
    String? lastUpdatedBy,
  }) =>
      SystemSettings(
        id: id ?? this.id,
        business: business ?? this.business,
        delivery: delivery ?? this.delivery,
        payment: payment ?? this.payment,
        notification: notification ?? this.notification,
        security: security ?? this.security,
        features: features ?? this.features,
        maintenance: maintenance ?? this.maintenance,
        lastUpdated: lastUpdated ?? this.lastUpdated,
        lastUpdatedBy: lastUpdatedBy ?? this.lastUpdatedBy,
      );
}

/// Business-related settings
class BusinessSettings {
  final String businessName;
  final String businessEmail;
  final String businessPhone;
  final String businessAddress;
  final double taxRate;
  final String currency;
  final String timezone;
  final List<String> supportedLanguages;
  final BusinessHours operatingHours;

  const BusinessSettings({
    required this.businessName,
    required this.businessEmail,
    required this.businessPhone,
    required this.businessAddress,
    required this.taxRate,
    required this.currency,
    required this.timezone,
    required this.supportedLanguages,
    required this.operatingHours,
  });

  factory BusinessSettings.defaultSettings() => BusinessSettings(
        businessName: 'Restaurant Hub',
        businessEmail: '<EMAIL>',
        businessPhone: '******-0123',
        businessAddress: '123 Main St, City, State 12345',
        taxRate: 0.08,
        currency: 'USD',
        timezone: 'America/New_York',
        supportedLanguages: ['en'],
        operatingHours: BusinessHours.defaultHours(),
      );

  factory BusinessSettings.fromJson(Map<String, dynamic> json) => BusinessSettings(
        businessName: json['businessName'],
        businessEmail: json['businessEmail'],
        businessPhone: json['businessPhone'],
        businessAddress: json['businessAddress'],
        taxRate: json['taxRate']?.toDouble() ?? 0.08,
        currency: json['currency'],
        timezone: json['timezone'],
        supportedLanguages: List<String>.from(json['supportedLanguages']),
        operatingHours: BusinessHours.fromJson(json['operatingHours']),
      );

  Map<String, dynamic> toJson() => {
        'businessName': businessName,
        'businessEmail': businessEmail,
        'businessPhone': businessPhone,
        'businessAddress': businessAddress,
        'taxRate': taxRate,
        'currency': currency,
        'timezone': timezone,
        'supportedLanguages': supportedLanguages,
        'operatingHours': operatingHours.toJson(),
      };
}

/// Delivery-related settings
class DeliverySettings {
  final double baseDeliveryFee;
  final double freeDeliveryThreshold;
  final double maxDeliveryDistance;
  final int estimatedDeliveryMinutes;
  final int maxDeliveryTimeMinutes;
  final bool enableRealTimeTracking;
  final bool enableScheduledDelivery;

  const DeliverySettings({
    required this.baseDeliveryFee,
    required this.freeDeliveryThreshold,
    required this.maxDeliveryDistance,
    required this.estimatedDeliveryMinutes,
    required this.maxDeliveryTimeMinutes,
    required this.enableRealTimeTracking,
    required this.enableScheduledDelivery,
  });

  factory DeliverySettings.defaultSettings() => const DeliverySettings(
        baseDeliveryFee: 3.99,
        freeDeliveryThreshold: 35.0,
        maxDeliveryDistance: 25.0,
        estimatedDeliveryMinutes: 45,
        maxDeliveryTimeMinutes: 120,
        enableRealTimeTracking: true,
        enableScheduledDelivery: false,
      );

  factory DeliverySettings.fromJson(Map<String, dynamic> json) => DeliverySettings(
        baseDeliveryFee: json['baseDeliveryFee']?.toDouble() ?? 3.99,
        freeDeliveryThreshold: json['freeDeliveryThreshold']?.toDouble() ?? 35.0,
        maxDeliveryDistance: json['maxDeliveryDistance']?.toDouble() ?? 25.0,
        estimatedDeliveryMinutes: json['estimatedDeliveryMinutes'] ?? 45,
        maxDeliveryTimeMinutes: json['maxDeliveryTimeMinutes'] ?? 120,
        enableRealTimeTracking: json['enableRealTimeTracking'] ?? true,
        enableScheduledDelivery: json['enableScheduledDelivery'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        'baseDeliveryFee': baseDeliveryFee,
        'freeDeliveryThreshold': freeDeliveryThreshold,
        'maxDeliveryDistance': maxDeliveryDistance,
        'estimatedDeliveryMinutes': estimatedDeliveryMinutes,
        'maxDeliveryTimeMinutes': maxDeliveryTimeMinutes,
        'enableRealTimeTracking': enableRealTimeTracking,
        'enableScheduledDelivery': enableScheduledDelivery,
      };
}

/// Payment-related settings
class PaymentSettings {
  final List<String> enabledPaymentMethods;
  final bool enableCashOnDelivery;
  final bool enableDigitalWallets;
  final bool enableCreditCards;
  final double minimumOrderAmount;
  final bool enableTipping;
  final List<double> suggestedTipPercentages;

  const PaymentSettings({
    required this.enabledPaymentMethods,
    required this.enableCashOnDelivery,
    required this.enableDigitalWallets,
    required this.enableCreditCards,
    required this.minimumOrderAmount,
    required this.enableTipping,
    required this.suggestedTipPercentages,
  });

  factory PaymentSettings.defaultSettings() => const PaymentSettings(
        enabledPaymentMethods: ['cash', 'credit_card', 'digital_wallet'],
        enableCashOnDelivery: true,
        enableDigitalWallets: true,
        enableCreditCards: true,
        minimumOrderAmount: 10.0,
        enableTipping: true,
        suggestedTipPercentages: [15.0, 18.0, 20.0, 25.0],
      );

  factory PaymentSettings.fromJson(Map<String, dynamic> json) => PaymentSettings(
        enabledPaymentMethods: List<String>.from(json['enabledPaymentMethods']),
        enableCashOnDelivery: json['enableCashOnDelivery'] ?? true,
        enableDigitalWallets: json['enableDigitalWallets'] ?? true,
        enableCreditCards: json['enableCreditCards'] ?? true,
        minimumOrderAmount: json['minimumOrderAmount']?.toDouble() ?? 10.0,
        enableTipping: json['enableTipping'] ?? true,
        suggestedTipPercentages: List<double>.from(json['suggestedTipPercentages']),
      );

  Map<String, dynamic> toJson() => {
        'enabledPaymentMethods': enabledPaymentMethods,
        'enableCashOnDelivery': enableCashOnDelivery,
        'enableDigitalWallets': enableDigitalWallets,
        'enableCreditCards': enableCreditCards,
        'minimumOrderAmount': minimumOrderAmount,
        'enableTipping': enableTipping,
        'suggestedTipPercentages': suggestedTipPercentages,
      };
}

/// Notification settings
class NotificationSettings {
  final bool enablePushNotifications;
  final bool enableEmailNotifications;
  final bool enableSmsNotifications;
  final bool enableOrderNotifications;
  final bool enablePromotionalNotifications;
  final bool enableSystemNotifications;

  const NotificationSettings({
    required this.enablePushNotifications,
    required this.enableEmailNotifications,
    required this.enableSmsNotifications,
    required this.enableOrderNotifications,
    required this.enablePromotionalNotifications,
    required this.enableSystemNotifications,
  });

  factory NotificationSettings.defaultSettings() => const NotificationSettings(
        enablePushNotifications: true,
        enableEmailNotifications: true,
        enableSmsNotifications: false,
        enableOrderNotifications: true,
        enablePromotionalNotifications: true,
        enableSystemNotifications: true,
      );

  factory NotificationSettings.fromJson(Map<String, dynamic> json) => NotificationSettings(
        enablePushNotifications: json['enablePushNotifications'] ?? true,
        enableEmailNotifications: json['enableEmailNotifications'] ?? true,
        enableSmsNotifications: json['enableSmsNotifications'] ?? false,
        enableOrderNotifications: json['enableOrderNotifications'] ?? true,
        enablePromotionalNotifications: json['enablePromotionalNotifications'] ?? true,
        enableSystemNotifications: json['enableSystemNotifications'] ?? true,
      );

  Map<String, dynamic> toJson() => {
        'enablePushNotifications': enablePushNotifications,
        'enableEmailNotifications': enableEmailNotifications,
        'enableSmsNotifications': enableSmsNotifications,
        'enableOrderNotifications': enableOrderNotifications,
        'enablePromotionalNotifications': enablePromotionalNotifications,
        'enableSystemNotifications': enableSystemNotifications,
      };
}

/// Security settings
class SecuritySettings {
  final int sessionTimeoutMinutes;
  final int maxLoginAttempts;
  final int lockoutDurationMinutes;
  final bool requireEmailVerification;
  final bool requirePhoneVerification;
  final bool enableTwoFactorAuth;
  final int passwordMinLength;
  final bool requirePasswordComplexity;

  const SecuritySettings({
    required this.sessionTimeoutMinutes,
    required this.maxLoginAttempts,
    required this.lockoutDurationMinutes,
    required this.requireEmailVerification,
    required this.requirePhoneVerification,
    required this.enableTwoFactorAuth,
    required this.passwordMinLength,
    required this.requirePasswordComplexity,
  });

  factory SecuritySettings.defaultSettings() => const SecuritySettings(
        sessionTimeoutMinutes: 30,
        maxLoginAttempts: 5,
        lockoutDurationMinutes: 15,
        requireEmailVerification: true,
        requirePhoneVerification: false,
        enableTwoFactorAuth: false,
        passwordMinLength: 8,
        requirePasswordComplexity: true,
      );

  factory SecuritySettings.fromJson(Map<String, dynamic> json) => SecuritySettings(
        sessionTimeoutMinutes: json['sessionTimeoutMinutes'] ?? 30,
        maxLoginAttempts: json['maxLoginAttempts'] ?? 5,
        lockoutDurationMinutes: json['lockoutDurationMinutes'] ?? 15,
        requireEmailVerification: json['requireEmailVerification'] ?? true,
        requirePhoneVerification: json['requirePhoneVerification'] ?? false,
        enableTwoFactorAuth: json['enableTwoFactorAuth'] ?? false,
        passwordMinLength: json['passwordMinLength'] ?? 8,
        requirePasswordComplexity: json['requirePasswordComplexity'] ?? true,
      );

  Map<String, dynamic> toJson() => {
        'sessionTimeoutMinutes': sessionTimeoutMinutes,
        'maxLoginAttempts': maxLoginAttempts,
        'lockoutDurationMinutes': lockoutDurationMinutes,
        'requireEmailVerification': requireEmailVerification,
        'requirePhoneVerification': requirePhoneVerification,
        'enableTwoFactorAuth': enableTwoFactorAuth,
        'passwordMinLength': passwordMinLength,
        'requirePasswordComplexity': requirePasswordComplexity,
      };
}

/// Feature flags for enabling/disabling features
class FeatureFlags {
  final bool enableSocialLogin;
  final bool enableGuestCheckout;
  final bool enableLoyaltyProgram;
  final bool enableReferralProgram;
  final bool enableReviews;
  final bool enableChat;
  final bool enableAnalytics;
  final bool enableA11y;

  const FeatureFlags({
    required this.enableSocialLogin,
    required this.enableGuestCheckout,
    required this.enableLoyaltyProgram,
    required this.enableReferralProgram,
    required this.enableReviews,
    required this.enableChat,
    required this.enableAnalytics,
    required this.enableA11y,
  });

  factory FeatureFlags.defaultFlags() => const FeatureFlags(
        enableSocialLogin: false,
        enableGuestCheckout: true,
        enableLoyaltyProgram: false,
        enableReferralProgram: false,
        enableReviews: true,
        enableChat: false,
        enableAnalytics: true,
        enableA11y: true,
      );

  factory FeatureFlags.fromJson(Map<String, dynamic> json) => FeatureFlags(
        enableSocialLogin: json['enableSocialLogin'] ?? false,
        enableGuestCheckout: json['enableGuestCheckout'] ?? true,
        enableLoyaltyProgram: json['enableLoyaltyProgram'] ?? false,
        enableReferralProgram: json['enableReferralProgram'] ?? false,
        enableReviews: json['enableReviews'] ?? true,
        enableChat: json['enableChat'] ?? false,
        enableAnalytics: json['enableAnalytics'] ?? true,
        enableA11y: json['enableA11y'] ?? true,
      );

  Map<String, dynamic> toJson() => {
        'enableSocialLogin': enableSocialLogin,
        'enableGuestCheckout': enableGuestCheckout,
        'enableLoyaltyProgram': enableLoyaltyProgram,
        'enableReferralProgram': enableReferralProgram,
        'enableReviews': enableReviews,
        'enableChat': enableChat,
        'enableAnalytics': enableAnalytics,
        'enableA11y': enableA11y,
      };
}

/// Maintenance settings
class MaintenanceSettings {
  final bool isMaintenanceMode;
  final String? maintenanceMessage;
  final DateTime? maintenanceStartTime;
  final DateTime? maintenanceEndTime;
  final List<String> allowedUserIds;

  const MaintenanceSettings({
    required this.isMaintenanceMode,
    this.maintenanceMessage,
    this.maintenanceStartTime,
    this.maintenanceEndTime,
    required this.allowedUserIds,
  });

  factory MaintenanceSettings.defaultSettings() => const MaintenanceSettings(
        isMaintenanceMode: false,
        allowedUserIds: [],
      );

  factory MaintenanceSettings.fromJson(Map<String, dynamic> json) => MaintenanceSettings(
        isMaintenanceMode: json['isMaintenanceMode'] ?? false,
        maintenanceMessage: json['maintenanceMessage'],
        maintenanceStartTime:
            json['maintenanceStartTime'] != null ? DateTime.parse(json['maintenanceStartTime']) : null,
        maintenanceEndTime: json['maintenanceEndTime'] != null ? DateTime.parse(json['maintenanceEndTime']) : null,
        allowedUserIds: List<String>.from(json['allowedUserIds'] ?? []),
      );

  Map<String, dynamic> toJson() => {
        'isMaintenanceMode': isMaintenanceMode,
        'maintenanceMessage': maintenanceMessage,
        'maintenanceStartTime': maintenanceStartTime?.toIso8601String(),
        'maintenanceEndTime': maintenanceEndTime?.toIso8601String(),
        'allowedUserIds': allowedUserIds,
      };
}

/// Business hours configuration
class BusinessHours {
  final Map<String, DayHours> weeklyHours;
  final List<Holiday> holidays;
  final bool isOpen24Hours;

  const BusinessHours({
    required this.weeklyHours,
    required this.holidays,
    required this.isOpen24Hours,
  });

  factory BusinessHours.defaultHours() => BusinessHours(
        weeklyHours: {
          'monday': DayHours(openTime: '09:00', closeTime: '22:00', isClosed: false),
          'tuesday': DayHours(openTime: '09:00', closeTime: '22:00', isClosed: false),
          'wednesday': DayHours(openTime: '09:00', closeTime: '22:00', isClosed: false),
          'thursday': DayHours(openTime: '09:00', closeTime: '22:00', isClosed: false),
          'friday': DayHours(openTime: '09:00', closeTime: '23:00', isClosed: false),
          'saturday': DayHours(openTime: '10:00', closeTime: '23:00', isClosed: false),
          'sunday': DayHours(openTime: '10:00', closeTime: '21:00', isClosed: false),
        },
        holidays: [],
        isOpen24Hours: false,
      );

  factory BusinessHours.fromJson(Map<String, dynamic> json) => BusinessHours(
        weeklyHours: Map<String, DayHours>.from(
          json['weeklyHours'].map((key, value) => MapEntry(key, DayHours.fromJson(value))),
        ),
        holidays: List<Holiday>.from(json['holidays'].map((x) => Holiday.fromJson(x))),
        isOpen24Hours: json['isOpen24Hours'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        'weeklyHours': weeklyHours.map((key, value) => MapEntry(key, value.toJson())),
        'holidays': holidays.map((x) => x.toJson()).toList(),
        'isOpen24Hours': isOpen24Hours,
      };
}

/// Day hours configuration
class DayHours {
  final String openTime;
  final String closeTime;
  final bool isClosed;

  const DayHours({
    required this.openTime,
    required this.closeTime,
    required this.isClosed,
  });

  factory DayHours.fromJson(Map<String, dynamic> json) => DayHours(
        openTime: json['openTime'],
        closeTime: json['closeTime'],
        isClosed: json['isClosed'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        'openTime': openTime,
        'closeTime': closeTime,
        'isClosed': isClosed,
      };
}

/// Holiday configuration
class Holiday {
  final String name;
  final DateTime date;
  final bool isRecurring;

  const Holiday({
    required this.name,
    required this.date,
    required this.isRecurring,
  });

  factory Holiday.fromJson(Map<String, dynamic> json) => Holiday(
        name: json['name'],
        date: DateTime.parse(json['date']),
        isRecurring: json['isRecurring'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'date': date.toIso8601String(),
        'isRecurring': isRecurring,
      };
}
