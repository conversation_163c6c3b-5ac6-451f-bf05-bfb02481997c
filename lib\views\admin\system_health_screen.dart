import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/system_monitoring_service.dart';
import '../shared/custom_app_bar.dart';

class SystemHealthScreen extends StatefulWidget {
  const SystemHealthScreen({super.key});

  @override
  State<SystemHealthScreen> createState() => _SystemHealthScreenState();
}

class _SystemHealthScreenState extends State<SystemHealthScreen> {
  final SystemMonitoringService _monitoringService = Get.find<SystemMonitoringService>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'System Health'),
      body: Obx(() {
        final health = _monitoringService.getSystemHealth();
        final alerts = _monitoringService.getUnacknowledgedAlerts();

        return RefreshIndicator(
          onRefresh: () async {
            _monitoringService.restartMonitoring();
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // System Status Overview
                _buildSystemStatusCard(health),
                const SizedBox(height: 24),

                // Performance Metrics
                _buildPerformanceMetrics(health),
                const SizedBox(height: 24),

                // Active Alerts
                if (alerts.isNotEmpty) ...[
                  _buildAlertsSection(alerts),
                  const SizedBox(height: 24),
                ],

                // Recent Performance History
                _buildPerformanceHistory(),
                const SizedBox(height: 24),

                // System Actions
                _buildSystemActions(),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildSystemStatusCard(Map<String, dynamic> health) {
    final isHealthy = health['healthy'] as bool;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  isHealthy ? Icons.check_circle : Icons.warning,
                  color: isHealthy ? Colors.green : Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isHealthy ? 'System Healthy' : 'System Issues Detected',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: isHealthy ? Colors.green : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      Text(
                        isHealthy ? 'All systems operating normally' : 'Some metrics require attention',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                Text(
                  'Last Updated: ${DateTime.now().toString().substring(11, 19)}',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics(Map<String, dynamic> health) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Metrics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
                child:
                    _buildMetricCard('CPU Usage', '${health['cpu'].toStringAsFixed(1)}%', health['cpu'], Icons.memory)),
            const SizedBox(width: 12),
            Expanded(
                child: _buildMetricCard(
                    'Memory', '${health['memory'].toStringAsFixed(1)}%', health['memory'], Icons.storage)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
                child: _buildMetricCard(
                    'Disk Usage', '${health['disk'].toStringAsFixed(1)}%', health['disk'], Icons.storage)),
            const SizedBox(width: 12),
            Expanded(
                child: _buildMetricCard('Response Time', '${health['responseTime'].toStringAsFixed(0)}ms',
                    health['responseTime'] / 10, Icons.speed)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildInfoCard('Active Connections', '${health['connections']}', Icons.link)),
            const SizedBox(width: 12),
            Expanded(child: _buildInfoCard('API Requests/min', '${health['apiRequests']}', Icons.api)),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, double percentage, IconData icon) {
    Color color = Colors.green;
    if (percentage > 80) {
      color = Colors.red;
    } else if (percentage > 60) {
      color = Colors.orange;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue),
                const SizedBox(width: 8),
                Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertsSection(List<Map<String, dynamic>> alerts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Active Alerts',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${alerts.length}',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...alerts.map((alert) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Icon(
                  Icons.warning,
                  color: _getAlertColor(alert['severity']),
                ),
                title: Text(alert['title']),
                subtitle: Text(alert['message']),
                trailing: TextButton(
                  onPressed: () => _monitoringService.acknowledgeAlert(alert['id']),
                  child: const Text('Acknowledge'),
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildPerformanceHistory() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance History (Last Hour)',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Text('Performance chart would be displayed here'),
                const SizedBox(height: 16),
                Text(
                  'In a production app, this would show real-time charts of CPU, memory, and response time metrics.',
                  style: TextStyle(color: Colors.grey.shade600),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSystemActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'System Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _monitoringService.restartMonitoring(),
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh Metrics'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _monitoringService.clearOldLogs(7),
                icon: const Icon(Icons.cleaning_services),
                label: const Text('Clear Old Logs'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getAlertColor(String severity) {
    switch (severity) {
      case 'critical':
        return Colors.red;
      case 'warning':
        return Colors.orange;
      case 'info':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
