import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/restaurant.dart';
import '../core/app_constants.dart';
import 'error_service.dart';
import 'audit_service.dart';

/// Comprehensive location service for GPS tracking and location-based features
class LocationService extends GetxService {
  static LocationService get instance => Get.find<LocationService>();

  // Current location
  final Rx<Position?> _currentPosition = Rx<Position?>(null);
  final RxString _currentAddress = ''.obs;
  final RxBool _isLocationEnabled = false.obs;
  final RxBool _hasLocationPermission = false.obs;
  
  // Location tracking
  final RxBool _isTracking = false.obs;
  StreamSubscription<Position>? _positionStream;
  final RxList<Position> _locationHistory = <Position>[].obs;
  
  // Delivery tracking
  final RxMap<String, Position> _driverLocations = <String, Position>{}.obs;
  final RxMap<String, List<Position>> _deliveryRoutes = <String, List<Position>>{}.obs;
  
  // Services
  late final ErrorService _errorService;
  late final AuditService _auditService;
  
  // Configuration
  static const LocationSettings locationSettings = LocationSettings(
    accuracy: LocationAccuracy.high,
    distanceFilter: 10, // Update every 10 meters
  );
  
  static const Duration locationTimeout = Duration(seconds: 15);
  static const double nearbyRadius = 5000; // 5km radius for nearby search
  
  // Getters
  Position? get currentPosition => _currentPosition.value;
  String get currentAddress => _currentAddress.value;
  bool get isLocationEnabled => _isLocationEnabled.value;
  bool get hasLocationPermission => _hasLocationPermission.value;
  bool get isTracking => _isTracking.value;
  List<Position> get locationHistory => _locationHistory;
  Map<String, Position> get driverLocations => _driverLocations;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeLocation();
  }

  @override
  void onClose() {
    _stopLocationTracking();
    super.onClose();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
    _auditService = Get.find<AuditService>();
  }

  Future<void> _initializeLocation() async {
    if (!AppConstants.enableLocationTracking) return;
    
    try {
      // Check if location services are enabled
      await _checkLocationService();
      
      // Request location permissions
      await _requestLocationPermission();
      
      // Get initial location if permission granted
      if (_hasLocationPermission.value) {
        await getCurrentLocation();
      }
      
      await _auditService.logAction(
        action: AuditAction.systemEvent,
        entityType: 'LocationService',
        reason: 'Location service initialized',
        severity: AuditSeverity.info,
      );
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.initialize');
    }
  }

  Future<void> _checkLocationService() async {
    try {
      _isLocationEnabled.value = await Geolocator.isLocationServiceEnabled();
      
      if (!_isLocationEnabled.value) {
        _errorService.handleAppError(
          'Location services are disabled',
          context: 'LocationService.checkLocationService',
        );
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.checkLocationService');
    }
  }

  Future<void> _requestLocationPermission() async {
    try {
      // Check current permission status
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      
      if (permission == LocationPermission.deniedForever) {
        // Show dialog to open app settings
        _showPermissionDialog();
        return;
      }
      
      _hasLocationPermission.value = permission == LocationPermission.whileInUse || 
                                    permission == LocationPermission.always;
      
      if (!_hasLocationPermission.value) {
        _errorService.handleAppError(
          'Location permission not granted',
          context: 'LocationService.requestLocationPermission',
        );
      }
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.requestLocationPermission');
    }
  }

  void _showPermissionDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Location Permission Required'),
        content: const Text(
          'This app needs location permission to provide location-based services. '
          'Please enable location permission in app settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Get current location
  Future<Position?> getCurrentLocation({bool forceRefresh = false}) async {
    if (!_isLocationEnabled.value || !_hasLocationPermission.value) {
      await _initializeLocation();
      if (!_hasLocationPermission.value) return null;
    }
    
    // Return cached position if not forcing refresh and position is recent
    if (!forceRefresh && _currentPosition.value != null) {
      final age = DateTime.now().difference(_currentPosition.value!.timestamp);
      if (age.inMinutes < 5) {
        return _currentPosition.value;
      }
    }
    
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: locationSettings,
      ).timeout(locationTimeout);
      
      _currentPosition.value = position;
      
      // Get address for the position
      await _updateCurrentAddress(position);
      
      // Add to history
      _addToLocationHistory(position);
      
      return position;
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.getCurrentLocation');
      return null;
    }
  }

  /// Start continuous location tracking
  Future<void> startLocationTracking() async {
    if (_isTracking.value) return;
    
    if (!_hasLocationPermission.value) {
      await _requestLocationPermission();
      if (!_hasLocationPermission.value) return;
    }
    
    try {
      _isTracking.value = true;
      
      _positionStream = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        _onLocationUpdate,
        onError: _onLocationError,
      );
      
      await _auditService.logAction(
        action: AuditAction.systemEvent,
        entityType: 'LocationService',
        reason: 'Location tracking started',
        severity: AuditSeverity.info,
      );
      
    } catch (e) {
      _isTracking.value = false;
      _errorService.handleAppError(e, context: 'LocationService.startLocationTracking');
    }
  }

  /// Stop location tracking
  void _stopLocationTracking() {
    if (!_isTracking.value) return;
    
    _positionStream?.cancel();
    _positionStream = null;
    _isTracking.value = false;
  }

  void _onLocationUpdate(Position position) {
    _currentPosition.value = position;
    _addToLocationHistory(position);
    
    // Update address periodically
    if (_locationHistory.length % 5 == 0) {
      _updateCurrentAddress(position);
    }
  }

  void _onLocationError(dynamic error) {
    _errorService.handleAppError(error, context: 'LocationService.locationStream');
    _stopLocationTracking();
  }

  void _addToLocationHistory(Position position) {
    _locationHistory.add(position);
    
    // Keep only recent locations (last 100)
    if (_locationHistory.length > 100) {
      _locationHistory.removeAt(0);
    }
  }

  Future<void> _updateCurrentAddress(Position position) async {
    try {
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        _currentAddress.value = _formatAddress(placemark);
      }
    } catch (e) {
      // Address lookup failed, but don't treat as critical error
      if (kDebugMode) {
        print('Address lookup failed: $e');
      }
    }
  }

  String _formatAddress(Placemark placemark) {
    final parts = <String>[];
    
    if (placemark.street?.isNotEmpty == true) parts.add(placemark.street!);
    if (placemark.locality?.isNotEmpty == true) parts.add(placemark.locality!);
    if (placemark.administrativeArea?.isNotEmpty == true) parts.add(placemark.administrativeArea!);
    if (placemark.postalCode?.isNotEmpty == true) parts.add(placemark.postalCode!);
    
    return parts.join(', ');
  }

  /// Calculate distance between two positions
  double calculateDistance(Position pos1, Position pos2) {
    return Geolocator.distanceBetween(
      pos1.latitude,
      pos1.longitude,
      pos2.latitude,
      pos2.longitude,
    );
  }

  /// Calculate distance from current location to a point
  double? calculateDistanceFromCurrent(double latitude, double longitude) {
    if (_currentPosition.value == null) return null;
    
    return calculateDistance(
      _currentPosition.value!,
      Position(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      ),
    );
  }

  /// Find nearby restaurants
  List<RestaurantModel> findNearbyRestaurants(
    List<RestaurantModel> restaurants, {
    double? maxDistance,
  }) {
    if (_currentPosition.value == null) return restaurants;
    
    final maxDist = maxDistance ?? nearbyRadius;
    final nearby = <RestaurantModel>[];
    
    for (final restaurant in restaurants) {
      final distance = calculateDistanceFromCurrent(
        restaurant.latitude,
        restaurant.longitude,
      );
      
      if (distance != null && distance <= maxDist) {
        nearby.add(restaurant);
      }
    }
    
    // Sort by distance
    nearby.sort((a, b) {
      final distA = calculateDistanceFromCurrent(a.latitude, a.longitude) ?? double.infinity;
      final distB = calculateDistanceFromCurrent(b.latitude, b.longitude) ?? double.infinity;
      return distA.compareTo(distB);
    });
    
    return nearby;
  }

  /// Get address from coordinates
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        return _formatAddress(placemarks.first);
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.getAddressFromCoordinates');
    }
    return null;
  }

  /// Get coordinates from address
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'LocationService.getCoordinatesFromAddress');
    }
    return null;
  }

  /// Update driver location (for real-time tracking)
  void updateDriverLocation(String driverId, Position position) {
    _driverLocations[driverId] = position;
    
    // Add to delivery route
    if (!_deliveryRoutes.containsKey(driverId)) {
      _deliveryRoutes[driverId] = <Position>[];
    }
    _deliveryRoutes[driverId]!.add(position);
    
    // Keep only recent route points (last 50)
    if (_deliveryRoutes[driverId]!.length > 50) {
      _deliveryRoutes[driverId]!.removeAt(0);
    }
  }

  /// Get estimated delivery time based on distance
  Duration getEstimatedDeliveryTime(double distanceInMeters) {
    // Assume average delivery speed of 30 km/h in city
    const averageSpeedKmh = 30.0;
    const averageSpeedMs = averageSpeedKmh * 1000 / 3600; // Convert to m/s
    
    final timeInSeconds = distanceInMeters / averageSpeedMs;
    
    // Add buffer time for preparation and traffic
    final bufferMinutes = 15 + (distanceInMeters / 1000 * 2); // 15 min base + 2 min per km
    
    return Duration(seconds: timeInSeconds.round() + (bufferMinutes * 60).round());
  }

  /// Check if location is within delivery area
  bool isWithinDeliveryArea(double latitude, double longitude) {
    if (_currentPosition.value == null) return false;
    
    final distance = calculateDistanceFromCurrent(latitude, longitude);
    return distance != null && distance <= AppConstants.maximumDeliveryDistance * 1000;
  }

  /// Get delivery fee based on distance
  double getDeliveryFee(double distanceInMeters) {
    if (distanceInMeters <= AppConstants.freeDeliveryThreshold * 1000) {
      return 0.0;
    }
    
    // Base fee + distance-based fee
    final distanceKm = distanceInMeters / 1000;
    final distanceFee = distanceKm * 0.5; // $0.50 per km
    
    return AppConstants.baseDeliveryFee + distanceFee;
  }

  /// Format distance for display
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      final km = distanceInMeters / 1000;
      return '${km.toStringAsFixed(1)}km';
    }
  }

  /// Format duration for display
  String formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}min';
    } else {
      return '${duration.inMinutes}min';
    }
  }
}
