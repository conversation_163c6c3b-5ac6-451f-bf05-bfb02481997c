import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/snackbars.dart';

class DriverEarningsScreen extends StatefulWidget {
  const DriverEarningsScreen({super.key});

  @override
  State<DriverEarningsScreen> createState() => _DriverEarningsScreenState();
}

class _DriverEarningsScreenState extends State<DriverEarningsScreen> with SingleTickerProviderStateMixin {
  final UserController _userController = Get.find<UserController>();

  late TabController _tabController;
  final List<String> _tabs = ['Today', 'Week', 'Month'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Earnings',
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabs.map((period) => _buildEarningsView(period)).toList(),
      ),
    );
  }

  Widget _buildEarningsView(String period) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Earnings Summary
          _buildEarningsSummary(period),
          // Earnings Breakdown
          _buildEarningsBreakdown(period),
          // Recent Deliveries
          _buildRecentDeliveries(period),
          // Payout Information
          _buildPayoutInfo(),
        ],
      ),
    );
  }

  Widget _buildEarningsSummary(String period) {
    final driver = _userController.currentUser;
    if (driver == null) return const SizedBox.shrink();

    // Mock data based on period
    final earnings = _getEarningsForPeriod(period);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$period\'s Earnings',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '\$${earnings['total'].toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Deliveries',
                  '${earnings['deliveries']}',
                  Icons.delivery_dining,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Tips',
                  '\$${earnings['tips'].toStringAsFixed(2)}',
                  Icons.star,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Avg/Order',
                  '\$${(earnings['total'] / earnings['deliveries']).toStringAsFixed(2)}',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEarningsBreakdown(String period) {
    final breakdown = _getEarningsBreakdown(period);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Earnings Breakdown',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Column(
              children: [
                _buildBreakdownItem(
                  'Base Pay',
                  '\$${breakdown['basePay'].toStringAsFixed(2)}',
                  Colors.blue,
                ),
                _buildBreakdownItem(
                  'Tips',
                  '\$${breakdown['tips'].toStringAsFixed(2)}',
                  Colors.green,
                ),
                _buildBreakdownItem(
                  'Bonuses',
                  '\$${breakdown['bonuses'].toStringAsFixed(2)}',
                  Colors.orange,
                ),
                const Divider(),
                _buildBreakdownItem(
                  'Total',
                  '\$${breakdown['total'].toStringAsFixed(2)}',
                  Theme.of(context).primaryColor,
                  isTotal: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownItem(String label, String amount, Color color, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentDeliveries(String period) {
    final deliveries = _getRecentDeliveries(period);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Deliveries',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          ...deliveries.map((delivery) => _buildDeliveryCard(delivery)),
        ],
      ),
    );
  }

  Widget _buildDeliveryCard(Map<String, dynamic> delivery) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.delivery_dining,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  delivery['restaurant'],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${delivery['distance']} • ${delivery['time']}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '\$${delivery['earnings'].toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              if (delivery['tip'] > 0) ...[
                const SizedBox(height: 2),
                Text(
                  '+\$${delivery['tip'].toStringAsFixed(2)} tip',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPayoutInfo() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payout Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.account_balance),
                  title: const Text('Bank Account'),
                  subtitle: const Text('****1234'),
                  trailing: TextButton(
                    onPressed: () {
                      InfoSnackBar.show('Bank account management coming soon!');
                    },
                    child: const Text('Change'),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('Next Payout'),
                  subtitle: const Text('Monday, 9:00 AM'),
                  trailing: const Text(
                    '\$125.50',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          PrimaryButton(
            text: 'Instant Payout (\$0.50 fee)',
            onPressed: () {
              _showInstantPayoutDialog();
            },
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getEarningsForPeriod(String period) {
    // Mock data - in real app this would come from API
    switch (period) {
      case 'Today':
        return {'total': 45.75, 'deliveries': 6, 'tips': 12.50};
      case 'Week':
        return {'total': 285.50, 'deliveries': 38, 'tips': 78.25};
      case 'Month':
        return {'total': 1250.75, 'deliveries': 165, 'tips': 325.50};
      default:
        return {'total': 0.0, 'deliveries': 0, 'tips': 0.0};
    }
  }

  Map<String, dynamic> _getEarningsBreakdown(String period) {
    final earnings = _getEarningsForPeriod(period);
    final total = earnings['total'];
    final tips = earnings['tips'];
    final basePay = total - tips - (total * 0.1); // Mock bonus calculation
    final bonuses = total * 0.1;

    return {
      'basePay': basePay,
      'tips': tips,
      'bonuses': bonuses,
      'total': total,
    };
  }

  List<Map<String, dynamic>> _getRecentDeliveries(String period) {
    // Mock data - in real app this would come from API
    return [
      {
        'restaurant': 'McDonald\'s',
        'distance': '2.5 km',
        'time': '25 min',
        'earnings': 8.50,
        'tip': 2.00,
      },
      {
        'restaurant': 'Pizza Hut',
        'distance': '1.8 km',
        'time': '18 min',
        'earnings': 7.25,
        'tip': 3.50,
      },
      {
        'restaurant': 'Subway',
        'distance': '3.2 km',
        'time': '32 min',
        'earnings': 9.75,
        'tip': 1.50,
      },
    ];
  }

  void _showInstantPayoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Instant Payout'),
        content: const Text(
          'Transfer \$125.00 to your bank account now?\n\nA \$0.50 fee will be deducted.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show('Payout initiated successfully!');
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}
