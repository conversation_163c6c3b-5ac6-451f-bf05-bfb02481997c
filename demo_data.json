{"users": [{"id": "user_001", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+1234567890", "profileImageUrl": "https://example.com/profiles/john_doe.jpg", "dateJoined": "2024-01-15T10:30:00Z", "isEmailVerified": true, "isPhoneVerified": true, "role": "customer", "lastLoginAt": null, "isActive": true, "department": null, "notes": null, "preferences": {"pushNotifications": true, "emailNotifications": true, "smsNotifications": false, "orderUpdates": true, "promotionalOffers": true, "language": "en", "currency": "USD", "darkMode": false}, "stats": {"totalOrders": 15, "totalSpent": 245.5, "favoriteRestaurants": 3, "lastOrderDate": "2024-07-01T18:45:00Z", "rate": {"id": "rate_001", "userId": "user_001", "restaureantId": "rest_001", "rating": 4.5, "comment": "Great food and fast delivery!", "createdAt": "2024-07-01T19:00:00Z"}, "address": {"id": "addr_001", "type": "Home", "fullAddress": "123 Main Street, Apt 4B, New York, NY 10001", "unit": "4B", "isDefault": true, "estimatedDelivery": "25-35 mins"}}}, {"id": "user_002", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+1987654321", "profileImageUrl": "https://example.com/profiles/sarah_johnson.jpg", "dateJoined": "2024-02-20T14:15:00Z", "isEmailVerified": true, "isPhoneVerified": false, "role": "customer", "lastLoginAt": null, "isActive": true, "department": null, "notes": null, "preferences": {"pushNotifications": true, "emailNotifications": false, "smsNotifications": true, "orderUpdates": true, "promotionalOffers": false, "language": "en", "currency": "USD", "darkMode": true}, "stats": {"totalOrders": 8, "totalSpent": 156.75, "favoriteRestaurants": 2, "lastOrderDate": "2024-06-28T20:30:00Z", "rate": {"id": "rate_002", "userId": "user_002", "restaureantId": "rest_002", "rating": 5.0, "comment": "Amazing pizza! Will order again.", "createdAt": "2024-06-28T21:00:00Z"}, "address": {"id": "addr_002", "type": "Work", "fullAddress": "456 Business Ave, Suite 200, New York, NY 10002", "unit": "Suite 200", "isDefault": true, "estimatedDelivery": "20-30 mins"}}}, {"id": "admin_001", "firstName": "<PERSON>", "lastName": "Admin", "email": "<EMAIL>", "phone": "+1555123456", "profileImageUrl": "https://example.com/profiles/admin.jpg", "dateJoined": "2023-12-01T09:00:00Z", "isEmailVerified": true, "isPhoneVerified": true, "role": "admin", "lastLoginAt": "2024-07-05T08:30:00Z", "isActive": true, "department": "Operations", "notes": "Senior administrator with full system access", "preferences": {"pushNotifications": true, "emailNotifications": true, "smsNotifications": true, "orderUpdates": true, "promotionalOffers": false, "language": "en", "currency": "USD", "darkMode": false}, "stats": {"totalOrders": 0, "totalSpent": 0.0, "favoriteRestaurants": 0, "lastOrderDate": null, "rate": {"id": "rate_admin_001", "userId": "admin_001", "restaureantId": "rest_001", "rating": 0.0, "comment": "", "createdAt": "2023-12-01T09:00:00Z"}, "address": {"id": "addr_admin_001", "type": "Office", "fullAddress": "789 Corporate Blvd, Floor 10, New York, NY 10003", "unit": "Floor 10", "isDefault": true, "estimatedDelivery": "15-25 mins"}}}, {"id": "rest_001", "firstName": "<PERSON>'s", "lastName": "Pizzeria", "email": "<EMAIL>", "phone": "+1555987654", "profileImageUrl": "https://example.com/restaurants/tonys_pizzeria.jpg", "dateJoined": "2024-01-10T12:00:00Z", "isEmailVerified": true, "isPhoneVerified": true, "role": "restaurant", "lastLoginAt": "2024-07-05T07:45:00Z", "isActive": true, "department": "Food Service", "notes": "Popular Italian restaurant specializing in authentic pizza and pasta", "preferences": {"pushNotifications": true, "emailNotifications": true, "smsNotifications": false, "orderUpdates": true, "promotionalOffers": true, "language": "en", "currency": "USD", "darkMode": false}, "stats": {"totalOrders": 0, "totalSpent": 0.0, "favoriteRestaurants": 0, "lastOrderDate": null, "rate": {"id": "rate_rest_001", "userId": "rest_001", "restaureantId": "rest_001", "rating": 4.7, "comment": "Highly rated restaurant", "createdAt": "2024-01-10T12:00:00Z"}, "address": {"id": "addr_rest_001", "type": "Restaurant", "fullAddress": "321 Food Street, New York, NY 10004", "unit": null, "isDefault": true, "estimatedDelivery": "30-45 mins"}}}, {"id": "rest_002", "firstName": "Burger", "lastName": "Palace", "email": "<EMAIL>", "phone": "+1555456789", "profileImageUrl": "https://example.com/restaurants/burger_palace.jpg", "dateJoined": "2024-01-20T15:30:00Z", "isEmailVerified": true, "isPhoneVerified": true, "role": "restaurant", "lastLoginAt": "2024-07-05T06:20:00Z", "isActive": true, "department": "Food Service", "notes": "Fast-casual burger joint with premium ingredients", "preferences": {"pushNotifications": true, "emailNotifications": true, "smsNotifications": true, "orderUpdates": true, "promotionalOffers": true, "language": "en", "currency": "USD", "darkMode": false}, "stats": {"totalOrders": 0, "totalSpent": 0.0, "favoriteRestaurants": 0, "lastOrderDate": null, "rate": {"id": "rate_rest_002", "userId": "rest_002", "restaureantId": "rest_002", "rating": 4.3, "comment": "Great burgers and fries", "createdAt": "2024-01-20T15:30:00Z"}, "address": {"id": "addr_rest_002", "type": "Restaurant", "fullAddress": "654 Burger Lane, New York, NY 10005", "unit": null, "isDefault": true, "estimatedDelivery": "25-40 mins"}}}, {"id": "driver_001", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+1555321654", "profileImageUrl": "https://example.com/profiles/carlos_driver.jpg", "dateJoined": "2024-03-01T11:00:00Z", "isEmailVerified": true, "isPhoneVerified": true, "role": "driver", "lastLoginAt": "2024-07-05T09:15:00Z", "isActive": true, "department": "Delivery", "notes": "Experienced delivery driver with excellent ratings", "preferences": {"pushNotifications": true, "emailNotifications": false, "smsNotifications": true, "orderUpdates": true, "promotionalOffers": false, "language": "en", "currency": "USD", "darkMode": true}, "stats": {"totalOrders": 0, "totalSpent": 0.0, "favoriteRestaurants": 0, "lastOrderDate": null, "rate": {"id": "rate_driver_001", "userId": "driver_001", "restaureantId": "rest_001", "rating": 4.9, "comment": "Fast and reliable delivery", "createdAt": "2024-03-01T11:00:00Z"}, "address": {"id": "addr_driver_001", "type": "Home", "fullAddress": "987 Driver Street, Apt 3A, New York, NY 10006", "unit": "3A", "isDefault": true, "estimatedDelivery": "10-20 mins"}}}], "menuItems": [{"id": "menu_001", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomato sauce, and basil", "price": 18.99, "image": "https://example.com/menu/margherita_pizza.jpg", "category": "Pizza", "isAvailable": true, "allergens": ["gluten", "dairy"]}, {"id": "menu_002", "name": "Pepperoni Pizza", "description": "Traditional pepperoni pizza with mozzarella cheese", "price": 21.99, "image": "https://example.com/menu/pepperoni_pizza.jpg", "category": "Pizza", "isAvailable": true, "allergens": ["gluten", "dairy"]}, {"id": "menu_003", "name": "<PERSON>", "description": "Fresh romaine lettuce with <PERSON> dressing, croutons, and parmesan", "price": 12.99, "image": "https://example.com/menu/caesar_salad.jpg", "category": "Salads", "isAvailable": true, "allergens": ["dairy", "eggs"]}, {"id": "menu_004", "name": "Classic Cheeseburger", "description": "Beef patty with cheese, lettuce, tomato, and special sauce", "price": 15.99, "image": "https://example.com/menu/cheeseburger.jpg", "category": "Burgers", "isAvailable": true, "allergens": ["gluten", "dairy"]}, {"id": "menu_005", "name": "BBQ Bacon Burger", "description": "Beef patty with BBQ sauce, bacon, onion rings, and cheese", "price": 18.99, "image": "https://example.com/menu/bbq_bacon_burger.jpg", "category": "Burgers", "isAvailable": true, "allergens": ["gluten", "dairy"]}, {"id": "menu_006", "name": "Sweet Potato Fries", "description": "Crispy sweet potato fries with sea salt", "price": 7.99, "image": "https://example.com/menu/sweet_potato_fries.jpg", "category": "Sides", "isAvailable": true, "allergens": []}, {"id": "menu_007", "name": "Chicken Alfredo Pasta", "description": "Grilled chicken with fettuccine in creamy alfredo sauce", "price": 19.99, "image": "https://example.com/menu/chicken_alfredo.jpg", "category": "Pasta", "isAvailable": false, "allergens": ["gluten", "dairy"]}, {"id": "menu_008", "name": "Chocolate Brownie", "description": "Rich chocolate brownie with vanilla ice cream", "price": 8.99, "image": "https://example.com/menu/chocolate_brownie.jpg", "category": "Desserts", "isAvailable": true, "allergens": ["gluten", "dairy", "eggs"]}], "paymentMethods": [{"id": "payment_001", "type": "creditCard", "displayName": "Visa ending in 1234", "cardNumber": "1234", "expiryDate": "12/26", "cardHolderName": "<PERSON>", "isDefault": true}, {"id": "payment_002", "type": "debitCard", "displayName": "Mastercard ending in 5678", "cardNumber": "5678", "expiryDate": "08/25", "cardHolderName": "<PERSON>", "isDefault": false}, {"id": "payment_003", "type": "paypal", "displayName": "PayPal Account", "cardNumber": null, "expiryDate": null, "cardHolderName": null, "isDefault": false}, {"id": "payment_004", "type": "applePay", "displayName": "Apple Pay", "cardNumber": null, "expiryDate": null, "cardHolderName": null, "isDefault": false}, {"id": "payment_005", "type": "cash", "displayName": "Cash on Delivery", "cardNumber": null, "expiryDate": null, "cardHolderName": null, "isDefault": false}], "cartItems": [{"id": "cart_001", "restaurantId": "rest_001", "quantity": 2, "specialInstructions": ["Extra cheese", "No onions"], "menuItem": {"id": "menu_001", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomato sauce, and basil", "price": 18.99, "image": "https://example.com/menu/margherita_pizza.jpg", "category": "Pizza", "isAvailable": true, "allergens": ["gluten", "dairy"]}}, {"id": "cart_002", "restaurantId": "rest_001", "quantity": 1, "specialInstructions": ["Dressing on the side"], "menuItem": {"id": "menu_003", "name": "<PERSON>", "description": "Fresh romaine lettuce with <PERSON> dressing, croutons, and parmesan", "price": 12.99, "image": "https://example.com/menu/caesar_salad.jpg", "category": "Salads", "isAvailable": true, "allergens": ["dairy", "eggs"]}}, {"id": "cart_003", "restaurantId": "rest_002", "quantity": 1, "specialInstructions": ["Medium rare", "No pickles"], "menuItem": {"id": "menu_004", "name": "Classic Cheeseburger", "description": "Beef patty with cheese, lettuce, tomato, and special sauce", "price": 15.99, "image": "https://example.com/menu/cheeseburger.jpg", "category": "Burgers", "isAvailable": true, "allergens": ["gluten", "dairy"]}}, {"id": "cart_004", "restaurantId": "rest_002", "quantity": 2, "specialInstructions": [], "menuItem": {"id": "menu_006", "name": "Sweet Potato Fries", "description": "Crispy sweet potato fries with sea salt", "price": 7.99, "image": "https://example.com/menu/sweet_potato_fries.jpg", "category": "Sides", "isAvailable": true, "allergens": []}}], "orders": [{"id": "order_001", "restaurantId": "rest_001", "deliveryFee": 3.99, "status": "delivered", "placedAt": "2024-07-01T18:45:00Z", "estimatedDeliveryTime": "2024-07-01T19:30:00Z", "deliveredAt": "2024-07-01T19:25:00Z", "driverId": "driver_001", "specialInstructions": "Ring doorbell twice", "paymentMethod": {"id": "payment_001", "type": "creditCard", "displayName": "Visa ending in 1234", "cardNumber": "1234", "expiryDate": "12/26", "cardHolderName": "<PERSON>", "isDefault": true}, "items": [{"id": "cart_001", "restaurantId": "rest_001", "quantity": 1, "specialInstructions": ["Extra cheese"], "menuItem": {"id": "menu_001", "name": "Margherita Pizza", "description": "Classic pizza with fresh mozzarella, tomato sauce, and basil", "price": 18.99, "image": "https://example.com/menu/margherita_pizza.jpg", "category": "Pizza", "isAvailable": true, "allergens": ["gluten", "dairy"]}}, {"id": "cart_002", "restaurantId": "rest_001", "quantity": 1, "specialInstructions": ["Dressing on the side"], "menuItem": {"id": "menu_003", "name": "<PERSON>", "description": "Fresh romaine lettuce with <PERSON> dressing, croutons, and parmesan", "price": 12.99, "image": "https://example.com/menu/caesar_salad.jpg", "category": "Salads", "isAvailable": true, "allergens": ["dairy", "eggs"]}}]}, {"id": "order_002", "restaurantId": "rest_002", "deliveryFee": 2.99, "status": "preparing", "placedAt": "2024-07-05T12:30:00Z", "estimatedDeliveryTime": "2024-07-05T13:15:00Z", "deliveredAt": null, "driverId": null, "specialInstructions": "Call when arriving", "paymentMethod": {"id": "payment_002", "type": "debitCard", "displayName": "Mastercard ending in 5678", "cardNumber": "5678", "expiryDate": "08/25", "cardHolderName": "<PERSON>", "isDefault": false}, "items": [{"id": "cart_003", "restaurantId": "rest_002", "quantity": 1, "specialInstructions": ["Medium rare", "No pickles"], "menuItem": {"id": "menu_004", "name": "Classic Cheeseburger", "description": "Beef patty with cheese, lettuce, tomato, and special sauce", "price": 15.99, "image": "https://example.com/menu/cheeseburger.jpg", "category": "Burgers", "isAvailable": true, "allergens": ["gluten", "dairy"]}}, {"id": "cart_004", "restaurantId": "rest_002", "quantity": 1, "specialInstructions": [], "menuItem": {"id": "menu_006", "name": "Sweet Potato Fries", "description": "Crispy sweet potato fries with sea salt", "price": 7.99, "image": "https://example.com/menu/sweet_potato_fries.jpg", "category": "Sides", "isAvailable": true, "allergens": []}}]}, {"id": "order_003", "restaurantId": "rest_001", "deliveryFee": 3.99, "status": "cancelled", "placedAt": "2024-06-28T20:30:00Z", "estimatedDeliveryTime": "2024-06-28T21:15:00Z", "deliveredAt": null, "driverId": null, "specialInstructions": null, "paymentMethod": {"id": "payment_003", "type": "paypal", "displayName": "PayPal Account", "cardNumber": null, "expiryDate": null, "cardHolderName": null, "isDefault": false}, "items": [{"id": "cart_005", "restaurantId": "rest_001", "quantity": 2, "specialInstructions": [], "menuItem": {"id": "menu_002", "name": "Pepperoni Pizza", "description": "Traditional pepperoni pizza with mozzarella cheese", "price": 21.99, "image": "https://example.com/menu/pepperoni_pizza.jpg", "category": "Pizza", "isAvailable": true, "allergens": ["gluten", "dairy"]}}]}]}