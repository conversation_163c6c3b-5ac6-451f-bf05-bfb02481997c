import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/menu_item.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../core/app_constants.dart';
import 'error_service.dart';
import 'audit_service.dart';

/// Comprehensive caching service for performance optimization
class CacheService extends GetxService {
  static CacheService get instance => Get.find<CacheService>();

  // Hive boxes for different data types
  Box<String>? _generalCache;
  Box<String>? _imageCache;
  Box<String>? _apiCache;
  Box<String>? _userDataCache;

  // Cache statistics
  final RxInt _cacheHits = 0.obs;
  final RxInt _cacheMisses = 0.obs;
  final RxInt _cacheSize = 0.obs;
  final RxDouble _cacheHitRatio = 0.0.obs;

  // Cache configuration
  final RxBool _isInitialized = false.obs;
  final RxBool _cacheEnabled = true.obs;

  // Services
  late final ErrorService _errorService;
  late final AuditService _auditService;

  // Cache settings
  static const Duration defaultCacheDuration = Duration(hours: 24);
  static const Duration imageCacheDuration = Duration(days: 7);
  static const Duration apiCacheDuration = Duration(minutes: 30);
  static const Duration userDataCacheDuration = Duration(days: 30);

  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxImageCacheSize = 50 * 1024 * 1024; // 50MB

  // Box names
  static const String generalBoxName = 'general_cache';
  static const String imageBoxName = 'image_cache';
  static const String apiBoxName = 'api_cache';
  static const String userDataBoxName = 'user_data_cache';

  // Getters
  bool get isInitialized => _isInitialized.value;
  bool get cacheEnabled => _cacheEnabled.value;
  int get cacheHits => _cacheHits.value;
  int get cacheMisses => _cacheMisses.value;
  int get cacheSize => _cacheSize.value;
  double get cacheHitRatio => _cacheHitRatio.value;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeCache();
  }

  @override
  void onClose() {
    _closeBoxes();
    super.onClose();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
    _auditService = Get.find<AuditService>();
  }

  Future<void> _initializeCache() async {
    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Open cache boxes
      _generalCache = await Hive.openBox<String>(generalBoxName);
      _imageCache = await Hive.openBox<String>(imageBoxName);
      _apiCache = await Hive.openBox<String>(apiBoxName);
      _userDataCache = await Hive.openBox<String>(userDataBoxName);

      // Load cache settings
      await _loadCacheSettings();

      // Update cache statistics
      _updateCacheStatistics();

      // Schedule cache cleanup
      _scheduleCacheCleanup();

      _isInitialized.value = true;

      await _auditService.logAction(
        action: AuditAction.systemEvent,
        entityType: 'CacheService',
        reason: 'Cache service initialized successfully',
        severity: AuditSeverity.info,
      );
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.initialize');
    }
  }

  Future<void> _loadCacheSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _cacheEnabled.value = prefs.getBool('cache_enabled') ?? true;
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.loadSettings');
    }
  }

  void _updateCacheStatistics() {
    if (!_isInitialized.value) return;

    try {
      int totalSize = 0;

      totalSize += _generalCache?.length ?? 0;
      totalSize += _imageCache?.length ?? 0;
      totalSize += _apiCache?.length ?? 0;
      totalSize += _userDataCache?.length ?? 0;

      _cacheSize.value = totalSize;

      final totalRequests = _cacheHits.value + _cacheMisses.value;
      _cacheHitRatio.value = totalRequests > 0 ? _cacheHits.value / totalRequests : 0.0;
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.updateStatistics');
    }
  }

  void _scheduleCacheCleanup() {
    // Schedule periodic cache cleanup
    Timer.periodic(const Duration(hours: 6), (timer) {
      _cleanupExpiredCache();
    });
  }

  // Generic cache operations
  Future<void> put(
    String key,
    dynamic value, {
    Duration? duration,
    CacheType type = CacheType.general,
  }) async {
    if (!_cacheEnabled.value || !_isInitialized.value) return;

    try {
      final box = _getBoxForType(type);
      if (box == null) return;

      final cacheItem = CacheItem(
        value: value,
        timestamp: DateTime.now(),
        duration: duration ?? _getDefaultDurationForType(type),
      );

      await box.put(key, jsonEncode(cacheItem.toJson()));
      _updateCacheStatistics();
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.put');
    }
  }

  Future<T?> get<T>(
    String key, {
    CacheType type = CacheType.general,
    T Function(dynamic)? fromJson,
  }) async {
    if (!_cacheEnabled.value || !_isInitialized.value) {
      _cacheMisses.value++;
      return null;
    }

    try {
      final box = _getBoxForType(type);
      if (box == null) {
        _cacheMisses.value++;
        return null;
      }

      final cachedData = box.get(key);
      if (cachedData == null) {
        _cacheMisses.value++;
        return null;
      }

      final cacheItem = CacheItem.fromJson(jsonDecode(cachedData));

      // Check if cache item has expired
      if (cacheItem.isExpired) {
        await box.delete(key);
        _cacheMisses.value++;
        return null;
      }

      _cacheHits.value++;
      _updateCacheStatistics();

      if (fromJson != null) {
        return fromJson(cacheItem.value);
      } else {
        return cacheItem.value as T;
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.get');
      _cacheMisses.value++;
      return null;
    }
  }

  Future<void> remove(String key, {CacheType type = CacheType.general}) async {
    if (!_isInitialized.value) return;

    try {
      final box = _getBoxForType(type);
      await box?.delete(key);
      _updateCacheStatistics();
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.remove');
    }
  }

  Future<void> clear({CacheType? type}) async {
    if (!_isInitialized.value) return;

    try {
      if (type != null) {
        final box = _getBoxForType(type);
        await box?.clear();
      } else {
        await _generalCache?.clear();
        await _imageCache?.clear();
        await _apiCache?.clear();
        await _userDataCache?.clear();
      }

      _updateCacheStatistics();
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.clear');
    }
  }

  // Specialized cache methods
  Future<void> cacheRestaurants(List<Map<String, dynamic>> restaurants) async {
    await put(
      'restaurants_list',
      restaurants,
      type: CacheType.api,
    );
  }

  Future<List<Map<String, dynamic>>?> getCachedRestaurants() async {
    return await get<List<Map<String, dynamic>>>(
      'restaurants_list',
      type: CacheType.api,
    );
  }

  Future<void> cacheRestaurant(Map<String, dynamic> restaurant) async {
    await put(
      'restaurant_${restaurant['id']}',
      restaurant,
      type: CacheType.api,
    );
  }

  Future<Map<String, dynamic>?> getCachedRestaurant(String restaurantId) async {
    return await get<Map<String, dynamic>>(
      'restaurant_$restaurantId',
      type: CacheType.api,
    );
  }

  Future<void> cacheMenuItems(String restaurantId, List<MenuItem> items) async {
    await put(
      'menu_$restaurantId',
      items.map((item) => item.toJson()).toList(),
      type: CacheType.api,
    );
  }

  Future<List<MenuItem>?> getCachedMenuItems(String restaurantId) async {
    final cached = await get<List<dynamic>>(
      'menu_$restaurantId',
      type: CacheType.api,
    );

    return cached?.map((json) => MenuItem.fromJson(json)).toList();
  }

  Future<void> cacheUserData(UserModel user) async {
    await put(
      'user_${user.id}',
      user.toJson(),
      type: CacheType.userData,
    );
  }

  Future<UserModel?> getCachedUserData(String userId) async {
    return await get<UserModel>(
      'user_$userId',
      type: CacheType.userData,
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  Future<void> cacheOrders(List<OrderModel> orders) async {
    await put(
      'user_orders',
      orders.map((order) => order.toJson()).toList(),
      type: CacheType.api,
    );
  }

  Future<List<OrderModel>?> getCachedOrders() async {
    final cached = await get<List<dynamic>>(
      'user_orders',
      type: CacheType.api,
    );

    return cached?.map((json) => OrderModel.fromJson(json)).toList();
  }

  // Image caching
  Future<void> cacheImageData(String url, Uint8List imageData) async {
    await put(
      'image_${url.hashCode}',
      base64Encode(imageData),
      type: CacheType.image,
      duration: imageCacheDuration,
    );
  }

  Future<Uint8List?> getCachedImageData(String url) async {
    final cached = await get<String>(
      'image_${url.hashCode}',
      type: CacheType.image,
    );

    if (cached != null) {
      try {
        return base64Decode(cached);
      } catch (e) {
        // Invalid base64 data, remove from cache
        await remove('image_${url.hashCode}', type: CacheType.image);
      }
    }

    return null;
  }

  // Cache management
  Future<void> _cleanupExpiredCache() async {
    if (!_isInitialized.value) return;

    try {
      await _cleanupBox(_generalCache);
      await _cleanupBox(_imageCache);
      await _cleanupBox(_apiCache);
      await _cleanupBox(_userDataCache);

      _updateCacheStatistics();
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.cleanupExpiredCache');
    }
  }

  Future<void> _cleanupBox(Box<String>? box) async {
    if (box == null) return;

    final keysToDelete = <String>[];

    for (final key in box.keys) {
      try {
        final cachedData = box.get(key);
        if (cachedData != null) {
          final cacheItem = CacheItem.fromJson(jsonDecode(cachedData));
          if (cacheItem.isExpired) {
            keysToDelete.add(key);
          }
        }
      } catch (e) {
        // Invalid cache item, mark for deletion
        keysToDelete.add(key);
      }
    }

    for (final key in keysToDelete) {
      await box.delete(key);
    }
  }

  Box<String>? _getBoxForType(CacheType type) {
    switch (type) {
      case CacheType.general:
        return _generalCache;
      case CacheType.image:
        return _imageCache;
      case CacheType.api:
        return _apiCache;
      case CacheType.userData:
        return _userDataCache;
    }
  }

  Duration _getDefaultDurationForType(CacheType type) {
    switch (type) {
      case CacheType.general:
        return defaultCacheDuration;
      case CacheType.image:
        return imageCacheDuration;
      case CacheType.api:
        return apiCacheDuration;
      case CacheType.userData:
        return userDataCacheDuration;
    }
  }

  // Settings management
  Future<void> setCacheEnabled(bool enabled) async {
    try {
      _cacheEnabled.value = enabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('cache_enabled', enabled);

      if (!enabled) {
        await clear();
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'CacheService.setCacheEnabled');
    }
  }

  // Cache statistics
  Map<String, dynamic> getCacheStatistics() {
    return {
      'hits': _cacheHits.value,
      'misses': _cacheMisses.value,
      'hitRatio': _cacheHitRatio.value,
      'size': _cacheSize.value,
      'enabled': _cacheEnabled.value,
    };
  }

  void _closeBoxes() {
    _generalCache?.close();
    _imageCache?.close();
    _apiCache?.close();
    _userDataCache?.close();
  }
}

/// Cache item wrapper with expiration
class CacheItem {
  final dynamic value;
  final DateTime timestamp;
  final Duration duration;

  CacheItem({
    required this.value,
    required this.timestamp,
    required this.duration,
  });

  bool get isExpired => DateTime.now().isAfter(timestamp.add(duration));

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration.inMilliseconds,
    };
  }

  factory CacheItem.fromJson(Map<String, dynamic> json) {
    return CacheItem(
      value: json['value'],
      timestamp: DateTime.parse(json['timestamp']),
      duration: Duration(milliseconds: json['duration']),
    );
  }
}

/// Cache type enumeration
enum CacheType {
  general,
  image,
  api,
  userData,
}

/// Performance monitoring service for tracking app performance
class PerformanceService extends GetxService {
  static PerformanceService get instance => Get.find<PerformanceService>();

  // Performance metrics
  final RxDouble _averageFrameTime = 0.0.obs;
  final RxInt _frameCount = 0.obs;
  final RxDouble _memoryUsage = 0.0.obs;
  final RxList<double> _frameTimes = <double>[].obs;
  final RxMap<String, double> _operationTimes = <String, double>{}.obs;

  // Performance tracking
  final Map<String, DateTime> _operationStartTimes = {};
  Timer? _performanceTimer;

  // Services
  late final ErrorService _errorService;

  // Getters
  double get averageFrameTime => _averageFrameTime.value;
  int get frameCount => _frameCount.value;
  double get memoryUsage => _memoryUsage.value;
  List<double> get frameTimes => _frameTimes;
  Map<String, double> get operationTimes => _operationTimes;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _startPerformanceMonitoring();
  }

  @override
  void onClose() {
    _performanceTimer?.cancel();
    super.onClose();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
  }

  void _startPerformanceMonitoring() {
    if (!AppConstants.enablePerformanceMonitoring) return;

    // Monitor performance every second
    _performanceTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updatePerformanceMetrics();
    });
  }

  void _updatePerformanceMetrics() {
    try {
      // Update frame count and average frame time
      _frameCount.value++;

      // Calculate average frame time (simplified)
      if (_frameTimes.isNotEmpty) {
        final sum = _frameTimes.reduce((a, b) => a + b);
        _averageFrameTime.value = sum / _frameTimes.length;
      }

      // Keep only recent frame times
      if (_frameTimes.length > 60) {
        _frameTimes.removeAt(0);
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'PerformanceService.updateMetrics');
    }
  }

  /// Start timing an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  /// End timing an operation and record the duration
  void endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime).inMilliseconds.toDouble();
      _operationTimes[operationName] = duration;
      _operationStartTimes.remove(operationName);
    }
  }

  /// Record a frame time
  void recordFrameTime(double frameTime) {
    _frameTimes.add(frameTime);
  }

  /// Get performance report
  Map<String, dynamic> getPerformanceReport() {
    return {
      'averageFrameTime': _averageFrameTime.value,
      'frameCount': _frameCount.value,
      'memoryUsage': _memoryUsage.value,
      'operationTimes': Map<String, double>.from(_operationTimes),
      'recentFrameTimes': List<double>.from(_frameTimes.take(10)),
    };
  }

  /// Reset performance metrics
  void resetMetrics() {
    _frameCount.value = 0;
    _averageFrameTime.value = 0.0;
    _frameTimes.clear();
    _operationTimes.clear();
    _operationStartTimes.clear();
  }
}
