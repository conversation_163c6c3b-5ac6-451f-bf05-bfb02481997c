import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/restaurant_controller.dart';
import '../../controllers/cart_controller.dart';
import '../../controllers/order_controller.dart';
import '../../core/app_routes.dart';
import '../../models/user.dart';
import '../../models/order.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/snackbars.dart' as snackbars;
import '../shared/empty_state_widget.dart';

class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  final UserController _userController = Get.find<UserController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final CartController _cartController = Get.find<CartController>();
  final OrderController _orderController = Get.find<OrderController>();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _restaurantController.loadRestaurants(),
      _orderController.loadUserOrders(),
    ]);
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) {
      snackbars.InfoSnackBar.show('Please enter a search term');
      return;
    }

    // Navigate to search results screen
    Get.toNamed(
      AppRoutes.searchResults,
      arguments: {'query': query.trim()},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: ProfileAppBar(
        title: 'Restaurant Hub',
        userName: _userController.currentUser?.fullName ?? 'User',
        userImageUrl: _userController.currentUser?.profileImageUrl,
        onProfileTap: () => Get.toNamed(AppRoutes.profile),
        actions: [
          Obx(() {
            final itemCount = _cartController.itemCount;
            return Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.shopping_cart_outlined),
                  onPressed: () => Get.toNamed(AppRoutes.cart),
                ),
                if (itemCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$itemCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(),
              // Quick Actions
              _buildQuickActions(),
              // Featured Restaurants
              _buildFeaturedRestaurants(),
              // Recent Orders
              _buildRecentOrders(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final user = _userController.currentUser;
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Hello, ${user?.firstName ?? 'User'}! 👋',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'What would you like to eat today?',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search for restaurants or food...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    // Get current text and search
                    final query = _searchController.text.trim();
                    if (query.isNotEmpty) {
                      _performSearch(query);
                    }
                  },
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              controller: _searchController,
              onSubmitted: (query) {
                _performSearch(query);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'My Orders',
                  subtitle: 'View order history',
                  icon: Icons.receipt_long,
                  onTap: () => Get.toNamed(AppRoutes.orders),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'Cart',
                  subtitle: 'View cart items',
                  icon: Icons.shopping_cart,
                  onTap: () => Get.toNamed(AppRoutes.cart),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedRestaurants() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Featured Restaurants',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  Get.toNamed(AppRoutes.restaurants);
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() {
            if (_restaurantController.isLoading) {
              return const LoadingWidget(message: 'Loading restaurants...');
            }

            if (_restaurantController.errorMessage.isNotEmpty) {
              return CustomErrorWidget(
                message: _restaurantController.errorMessage,
                onRetry: _loadData,
              );
            }

            final restaurants = _restaurantController.restaurants;
            if (restaurants.isEmpty) {
              return const EmptyStateWidget(
                title: 'No Restaurants',
                message: 'No restaurants available at the moment.',
                icon: Icons.restaurant,
              );
            }

            return SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: restaurants.length,
                itemBuilder: (context, index) {
                  final restaurant = restaurants[index];
                  return _buildRestaurantCard(restaurant);
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRestaurantCard(UserModel restaurant) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 12),
      child: CustomCard(
        onTap: () {
          _restaurantController.selectRestaurant(restaurant);
          Get.toNamed(AppRoutes.menu);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Icon(
                  Icons.restaurant,
                  size: 40,
                  color: Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              restaurant.fullName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.star, size: 14, color: Colors.orange),
                const SizedBox(width: 4),
                Text(
                  restaurant.stats.rate.rating.toStringAsFixed(1),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              restaurant.stats.address.estimatedDelivery,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentOrders() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Orders',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Obx(() {
            if (_orderController.isLoading) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (_orderController.hasError) {
              return CustomErrorWidget(
                message: _orderController.errorMessage,
                onRetry: () => _orderController.loadUserOrders(),
              );
            }

            final recentOrders = _orderController.userOrders.take(3).toList();

            if (recentOrders.isEmpty) {
              return const EmptyStateWidget(
                title: 'No Recent Orders',
                message: 'You haven\'t placed any orders yet.',
                icon: Icons.receipt_long,
                actionText: 'Browse Restaurants',
              );
            }

            return Column(
              children: [
                ...recentOrders.map((order) => _buildRecentOrderCard(order)),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => Get.toNamed(AppRoutes.orders),
                  child: const Text('View All Orders'),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRecentOrderCard(OrderModel order) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => Get.toNamed(AppRoutes.orderDetails, arguments: order.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Order Status Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getOrderStatusColor(order.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getOrderStatusIcon(order.status),
                  color: _getOrderStatusColor(order.status),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              // Order Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order #${order.id.substring(0, 8)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getRestaurantName(order.restaurantId),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _formatOrderDate(order.placedAt),
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              // Order Status and Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getOrderStatusColor(order.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getOrderStatusText(order.status),
                      style: TextStyle(
                        color: _getOrderStatusColor(order.status),
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${_calculateOrderTotal(order).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getOrderStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.grey;
      case OrderStatus.placed:
        return Colors.blue;
      case OrderStatus.confirmed:
        return Colors.orange;
      case OrderStatus.preparing:
        return Colors.amber;
      case OrderStatus.ready:
        return Colors.purple;
      case OrderStatus.pickedUp:
        return Colors.indigo;
      case OrderStatus.onTheWay:
        return Colors.teal;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getOrderStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.hourglass_empty;
      case OrderStatus.placed:
        return Icons.receipt;
      case OrderStatus.confirmed:
        return Icons.check_circle_outline;
      case OrderStatus.preparing:
        return Icons.restaurant;
      case OrderStatus.ready:
        return Icons.done_all;
      case OrderStatus.pickedUp:
        return Icons.local_shipping;
      case OrderStatus.onTheWay:
        return Icons.delivery_dining;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getOrderStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.placed:
        return 'Placed';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.onTheWay:
        return 'On the Way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _getRestaurantName(String restaurantId) {
    final restaurant = _restaurantController.restaurants.firstWhereOrNull((r) => r.id == restaurantId);
    return restaurant?.fullName ?? 'Unknown Restaurant';
  }

  String _formatOrderDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  double _calculateOrderTotal(OrderModel order) {
    final itemsTotal = order.items.fold(0.0, (sum, item) => sum + (item.menuItem.price * item.quantity));
    return itemsTotal + order.deliveryFee;
  }
}
