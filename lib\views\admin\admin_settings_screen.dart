import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/snackbars.dart';
import 'audit_logs_screen.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  // Settings state
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _maintenanceMode = false;
  bool _allowNewRegistrations = true;
  bool _requireEmailVerification = true;
  String _selectedTheme = 'System';
  String _selectedLanguage = 'English';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'Admin Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSystemSettings(),
            const SizedBox(height: 24),
            _buildNotificationSettings(),
            const SizedBox(height: 24),
            _buildAppearanceSettings(),
            const SizedBox(height: 24),
            _buildSecuritySettings(),
            const SizedBox(height: 24),
            _buildAuditSettings(),
            const SizedBox(height: 24),
            _buildDataManagement(),
            const SizedBox(height: 24),
            _buildDangerZone(),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildSwitchTile(
            title: 'Maintenance Mode',
            subtitle: 'Temporarily disable the app for maintenance',
            value: _maintenanceMode,
            onChanged: (value) {
              setState(() {
                _maintenanceMode = value;
              });
              _showMaintenanceModeDialog(value);
            },
            icon: Icons.build,
          ),
          _buildSwitchTile(
            title: 'Allow New Registrations',
            subtitle: 'Enable new users to register accounts',
            value: _allowNewRegistrations,
            onChanged: (value) {
              setState(() {
                _allowNewRegistrations = value;
              });
              _saveSettings();
            },
            icon: Icons.person_add,
          ),
          _buildSwitchTile(
            title: 'Require Email Verification',
            subtitle: 'New users must verify their email address',
            value: _requireEmailVerification,
            onChanged: (value) {
              setState(() {
                _requireEmailVerification = value;
              });
              _saveSettings();
            },
            icon: Icons.verified_user,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildSwitchTile(
            title: 'Email Notifications',
            subtitle: 'Receive admin alerts via email',
            value: _emailNotifications,
            onChanged: (value) {
              setState(() {
                _emailNotifications = value;
              });
              _saveSettings();
            },
            icon: Icons.email,
          ),
          _buildSwitchTile(
            title: 'Push Notifications',
            subtitle: 'Receive admin alerts as push notifications',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
              _saveSettings();
            },
            icon: Icons.notifications,
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Appearance',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildDropdownTile(
            title: 'Theme',
            subtitle: 'Choose app theme',
            value: _selectedTheme,
            items: ['Light', 'Dark', 'System'],
            onChanged: (value) {
              setState(() {
                _selectedTheme = value!;
              });
              _saveSettings();
            },
            icon: Icons.palette,
          ),
          _buildDropdownTile(
            title: 'Language',
            subtitle: 'Choose app language',
            value: _selectedLanguage,
            items: ['English', 'Spanish', 'French', 'German'],
            onChanged: (value) {
              setState(() {
                _selectedLanguage = value!;
              });
              _saveSettings();
            },
            icon: Icons.language,
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Security',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Change Password',
            subtitle: 'Update your admin password',
            icon: Icons.lock,
            onTap: _changePassword,
          ),
          _buildActionTile(
            title: 'Two-Factor Authentication',
            subtitle: 'Enable 2FA for enhanced security',
            icon: Icons.security,
            onTap: _setup2FA,
          ),
          _buildActionTile(
            title: 'Active Sessions',
            subtitle: 'View and manage active login sessions',
            icon: Icons.devices,
            onTap: _viewActiveSessions,
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagement() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Management',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Export Data',
            subtitle: 'Download system data as CSV/JSON',
            icon: Icons.download,
            onTap: _exportData,
          ),
          _buildActionTile(
            title: 'Import Data',
            subtitle: 'Import data from external sources',
            icon: Icons.upload,
            onTap: _importData,
          ),
          _buildActionTile(
            title: 'Database Backup',
            subtitle: 'Create a backup of the database',
            icon: Icons.backup,
            onTap: _createBackup,
          ),
        ],
      ),
    );
  }

  Widget _buildAuditSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Audit & Monitoring',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'View Audit Logs',
            subtitle: 'Review system activity and admin actions',
            icon: Icons.history,
            onTap: () => Get.to(() => const AuditLogsScreen()),
          ),
          _buildActionTile(
            title: 'System Health',
            subtitle: 'Monitor system performance and health',
            icon: Icons.monitor_heart,
            onTap: () => Get.toNamed('/system-health'),
          ),
          _buildActionTile(
            title: 'System Analytics',
            subtitle: 'View detailed system metrics',
            icon: Icons.analytics,
            onTap: () => Get.toNamed('/admin/analytics'),
          ),
          _buildActionTile(
            title: 'Security Monitoring',
            subtitle: 'Monitor security events and threats',
            icon: Icons.security,
            onTap: () => _showSecurityMonitoring(),
          ),
        ],
      ),
    );
  }

  Widget _buildDangerZone() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Danger Zone',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Clear Cache',
            subtitle: 'Clear all cached data',
            icon: Icons.clear_all,
            onTap: _clearCache,
            textColor: Colors.orange,
          ),
          _buildActionTile(
            title: 'Reset Settings',
            subtitle: 'Reset all settings to default',
            icon: Icons.restore,
            onTap: _resetSettings,
            textColor: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor ?? Theme.of(context).primaryColor),
      title: Text(
        title,
        style: TextStyle(color: textColor),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showMaintenanceModeDialog(bool enable) {
    Get.dialog(
      AlertDialog(
        title: Text(enable ? 'Enable Maintenance Mode' : 'Disable Maintenance Mode'),
        content: Text(
          enable
              ? 'This will temporarily disable the app for all users. Are you sure?'
              : 'This will re-enable the app for all users.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _maintenanceMode = !enable;
              });
              Get.back();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _saveSettings();
              SuccessSnackBar.show(
                enable ? 'Maintenance mode enabled' : 'Maintenance mode disabled',
              );
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    // In a real app, this would save settings to the backend
    SuccessSnackBar.show('Settings saved successfully');
  }

  void _changePassword() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Change Password'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: currentPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Current Password',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: newPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'New Password',
                  border: OutlineInputBorder(),
                  helperText: 'Must be at least 8 characters with uppercase, lowercase, and numbers',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Confirm New Password',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_validatePasswordChange(
                currentPasswordController.text,
                newPasswordController.text,
                confirmPasswordController.text,
              )) {
                _performPasswordChange(newPasswordController.text);
                Get.back();
              }
            },
            child: const Text('Change Password'),
          ),
        ],
      ),
    );
  }

  bool _validatePasswordChange(String current, String newPassword, String confirm) {
    if (current.isEmpty || newPassword.isEmpty || confirm.isEmpty) {
      ErrorSnackBar.show('All fields are required');
      return false;
    }

    if (newPassword != confirm) {
      ErrorSnackBar.show('New passwords do not match');
      return false;
    }

    if (newPassword.length < 8) {
      ErrorSnackBar.show('Password must be at least 8 characters long');
      return false;
    }

    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(newPassword)) {
      ErrorSnackBar.show('Password must contain uppercase, lowercase, and numbers');
      return false;
    }

    return true;
  }

  void _performPasswordChange(String newPassword) {
    // In a real app, this would call the authentication service
    SuccessSnackBar.show('Password changed successfully');
  }

  void _setup2FA() {
    Get.dialog(
      AlertDialog(
        title: const Text('Two-Factor Authentication'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enhance your account security by enabling two-factor authentication.',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              const Text(
                'Setup Methods:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              ListTile(
                leading: const Icon(Icons.phone_android, color: Colors.blue),
                title: const Text('Authenticator App'),
                subtitle: const Text('Google Authenticator, Authy, etc.'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Get.back();
                  _setupAuthenticatorApp();
                },
              ),
              ListTile(
                leading: const Icon(Icons.sms, color: Colors.green),
                title: const Text('SMS Verification'),
                subtitle: const Text('Receive codes via text message'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Get.back();
                  _setupSMSVerification();
                },
              ),
              ListTile(
                leading: const Icon(Icons.email, color: Colors.orange),
                title: const Text('Email Verification'),
                subtitle: const Text('Receive codes via email'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Get.back();
                  _setupEmailVerification();
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _setupAuthenticatorApp() {
    Get.dialog(
      AlertDialog(
        title: const Text('Setup Authenticator App'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('1. Install an authenticator app on your phone'),
              const SizedBox(height: 12),
              const Text('2. Scan this QR code with your app:'),
              const SizedBox(height: 16),
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text(
                    'QR Code\n(Demo)',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text('3. Enter the 6-digit code from your app:'),
              const SizedBox(height: 12),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Verification Code',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                maxLength: 6,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show('2FA enabled successfully with authenticator app');
            },
            child: const Text('Enable 2FA'),
          ),
        ],
      ),
    );
  }

  void _setupSMSVerification() {
    final phoneController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Setup SMS Verification'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Enter your phone number to receive verification codes:'),
              const SizedBox(height: 16),
              TextField(
                controller: phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  border: OutlineInputBorder(),
                  prefixText: '+1 ',
                ),
                keyboardType: TextInputType.phone,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (phoneController.text.isNotEmpty) {
                Get.back();
                SuccessSnackBar.show('2FA enabled successfully with SMS verification');
              } else {
                ErrorSnackBar.show('Please enter a valid phone number');
              }
            },
            child: const Text('Enable 2FA'),
          ),
        ],
      ),
    );
  }

  void _setupEmailVerification() {
    Get.dialog(
      AlertDialog(
        title: const Text('Setup Email Verification'),
        content: const SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Two-factor authentication codes will be sent to your registered email address.'),
              SizedBox(height: 16),
              Text(
                'Current email: <EMAIL>',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show('2FA enabled successfully with email verification');
            },
            child: const Text('Enable 2FA'),
          ),
        ],
      ),
    );
  }

  void _viewActiveSessions() {
    final sessions = _getDemoSessions();

    Get.dialog(
      AlertDialog(
        title: const Text('Active Sessions'),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Column(
            children: [
              const Text(
                'Manage your active login sessions across different devices and locations.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: sessions.length,
                  itemBuilder: (context, index) {
                    final session = sessions[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Icon(
                          session['isCurrent'] ? Icons.computer : _getDeviceIcon(session['device']),
                          color: session['isCurrent'] ? Colors.green : Colors.grey,
                        ),
                        title: Text(session['device']),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Location: ${session['location']}'),
                            Text('Last Active: ${session['lastActive']}'),
                            Text('IP: ${session['ip']}'),
                          ],
                        ),
                        trailing: session['isCurrent']
                            ? const Chip(
                                label: Text('Current'),
                                backgroundColor: Colors.green,
                                labelStyle: TextStyle(color: Colors.white),
                              )
                            : IconButton(
                                icon: const Icon(Icons.logout, color: Colors.red),
                                onPressed: () => _terminateSession(session['id']),
                              ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () => _terminateAllOtherSessions(),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Terminate All Others'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoSessions() {
    return [
      {
        'id': 'session_1',
        'device': 'Windows PC - Chrome',
        'location': 'New York, USA',
        'lastActive': '2 minutes ago',
        'ip': '*************',
        'isCurrent': true,
      },
      {
        'id': 'session_2',
        'device': 'iPhone 14 - Safari',
        'location': 'New York, USA',
        'lastActive': '1 hour ago',
        'ip': '*************',
        'isCurrent': false,
      },
      {
        'id': 'session_3',
        'device': 'MacBook Pro - Chrome',
        'location': 'San Francisco, USA',
        'lastActive': '3 days ago',
        'ip': '*********',
        'isCurrent': false,
      },
    ];
  }

  IconData _getDeviceIcon(String device) {
    if (device.contains('iPhone') || device.contains('Android')) {
      return Icons.phone_android;
    } else if (device.contains('iPad') || device.contains('Tablet')) {
      return Icons.tablet;
    } else if (device.contains('Mac')) {
      return Icons.laptop_mac;
    } else {
      return Icons.computer;
    }
  }

  void _terminateSession(String sessionId) {
    Get.dialog(
      AlertDialog(
        title: const Text('Terminate Session'),
        content:
            const Text('Are you sure you want to terminate this session? The user will be logged out immediately.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.back(); // Close sessions dialog
              SuccessSnackBar.show('Session terminated successfully');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Terminate'),
          ),
        ],
      ),
    );
  }

  void _terminateAllOtherSessions() {
    Get.dialog(
      AlertDialog(
        title: const Text('Terminate All Other Sessions'),
        content: const Text('This will log you out from all other devices. You will remain logged in on this device.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.back(); // Close sessions dialog
              SuccessSnackBar.show('All other sessions terminated successfully');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Terminate All'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    Get.dialog(
      AlertDialog(
        title: const Text('Export Data'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select the data you want to export:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('User Data'),
                subtitle: const Text('All user accounts and profiles'),
                value: true,
                onChanged: (value) {},
              ),
              CheckboxListTile(
                title: const Text('Restaurant Data'),
                subtitle: const Text('Restaurant profiles and menus'),
                value: true,
                onChanged: (value) {},
              ),
              CheckboxListTile(
                title: const Text('Order History'),
                subtitle: const Text('All order transactions'),
                value: true,
                onChanged: (value) {},
              ),
              CheckboxListTile(
                title: const Text('Audit Logs'),
                subtitle: const Text('System activity logs'),
                value: false,
                onChanged: (value) {},
              ),
              const SizedBox(height: 16),
              const Text(
                'Export Format:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('JSON'),
                      value: 'json',
                      groupValue: 'json',
                      onChanged: (value) {},
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('CSV'),
                      value: 'csv',
                      groupValue: 'json',
                      onChanged: (value) {},
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performDataExport();
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _performDataExport() {
    // Simulate export process
    Get.dialog(
      AlertDialog(
        title: const Text('Exporting Data'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Preparing your data export...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    // Simulate export delay
    Future.delayed(const Duration(seconds: 3), () {
      Get.back(); // Close progress dialog
      SuccessSnackBar.show('Data exported successfully! Download link sent to your email.');
    });
  }

  void _importData() {
    Get.dialog(
      AlertDialog(
        title: const Text('Import Data'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Import data from external sources:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey, style: BorderStyle.solid),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.cloud_upload, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('Drag and drop files here'),
                    Text('or click to browse', style: TextStyle(color: Colors.blue)),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Supported formats: JSON, CSV, XML',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
              const SizedBox(height: 16),
              const Text(
                'Import Options:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              CheckboxListTile(
                title: const Text('Merge with existing data'),
                subtitle: const Text('Keep existing records and add new ones'),
                value: true,
                onChanged: (value) {},
              ),
              CheckboxListTile(
                title: const Text('Validate data before import'),
                subtitle: const Text('Check for errors and conflicts'),
                value: true,
                onChanged: (value) {},
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performDataImport();
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _performDataImport() {
    // Simulate import process
    Get.dialog(
      AlertDialog(
        title: const Text('Importing Data'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Processing your data import...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    // Simulate import delay
    Future.delayed(const Duration(seconds: 4), () {
      Get.back(); // Close progress dialog
      SuccessSnackBar.show('Data imported successfully! 150 records processed.');
    });
  }

  void _createBackup() {
    Get.dialog(
      AlertDialog(
        title: const Text('Create Database Backup'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Create a complete backup of your database:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('Backup will include:'),
              const SizedBox(height: 8),
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('All user accounts and profiles'),
                ],
              ),
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('Restaurant data and menus'),
                ],
              ),
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('Order history and transactions'),
                ],
              ),
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('System settings and configurations'),
                ],
              ),
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('Audit logs and security data'),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Backup will be encrypted and stored securely. You will receive a download link via email.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performDatabaseBackup();
            },
            child: const Text('Create Backup'),
          ),
        ],
      ),
    );
  }

  void _performDatabaseBackup() {
    // Simulate backup process
    Get.dialog(
      AlertDialog(
        title: const Text('Creating Backup'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Creating encrypted database backup...'),
            SizedBox(height: 8),
            Text('This may take a few minutes', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    // Simulate backup delay
    Future.delayed(const Duration(seconds: 5), () {
      Get.back(); // Close progress dialog
      SuccessSnackBar.show('Database backup created successfully! Download link sent to your email.');
    });
  }

  void _showSecurityMonitoring() {
    final securityEvents = _getDemoSecurityEvents();

    Get.dialog(
      AlertDialog(
        title: const Text('Security Monitoring'),
        content: SizedBox(
          width: 700,
          height: 500,
          child: Column(
            children: [
              // Security Status Overview
              Row(
                children: [
                  Expanded(
                    child: Card(
                      color: Colors.green.shade50,
                      child: const Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Icon(Icons.security, color: Colors.green, size: 32),
                            SizedBox(height: 8),
                            Text('System Secure', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('No active threats', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Card(
                      color: Colors.orange.shade50,
                      child: const Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Icon(Icons.warning, color: Colors.orange, size: 32),
                            SizedBox(height: 8),
                            Text('3 Warnings', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('Requires attention', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Card(
                      color: Colors.blue.shade50,
                      child: const Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Icon(Icons.shield, color: Colors.blue, size: 32),
                            SizedBox(height: 8),
                            Text('Firewall Active', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('All ports protected', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Recent Security Events',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: ListView.builder(
                  itemCount: securityEvents.length,
                  itemBuilder: (context, index) {
                    final event = securityEvents[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Icon(
                          _getSecurityIcon(event['type']),
                          color: _getSecurityColor(event['severity']),
                        ),
                        title: Text(event['title']),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(event['description']),
                            Text(
                              '${event['timestamp']} • ${event['source']}',
                              style: const TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                          ],
                        ),
                        trailing: Chip(
                          label: Text(event['severity']),
                          backgroundColor: _getSecurityColor(event['severity']).withOpacity(0.1),
                          labelStyle: TextStyle(color: _getSecurityColor(event['severity'])),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () => _refreshSecurityStatus(),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoSecurityEvents() {
    return [
      {
        'type': 'login_attempt',
        'title': 'Failed Login Attempt',
        'description': 'Multiple failed login attempts from IP *************',
        'severity': 'Warning',
        'timestamp': '2 minutes ago',
        'source': 'Authentication System',
      },
      {
        'type': 'password_change',
        'title': 'Password Changed',
        'description': 'Admin user changed password successfully',
        'severity': 'Info',
        'timestamp': '1 hour ago',
        'source': 'User Management',
      },
      {
        'type': 'suspicious_activity',
        'title': 'Unusual API Activity',
        'description': 'High number of API requests from single IP',
        'severity': 'Warning',
        'timestamp': '3 hours ago',
        'source': 'API Gateway',
      },
      {
        'type': 'system_update',
        'title': 'Security Update Applied',
        'description': 'System security patches installed successfully',
        'severity': 'Info',
        'timestamp': '1 day ago',
        'source': 'System Maintenance',
      },
      {
        'type': 'firewall',
        'title': 'Firewall Rule Updated',
        'description': 'New firewall rule added to block suspicious IPs',
        'severity': 'Info',
        'timestamp': '2 days ago',
        'source': 'Network Security',
      },
    ];
  }

  IconData _getSecurityIcon(String type) {
    switch (type) {
      case 'login_attempt':
        return Icons.login;
      case 'password_change':
        return Icons.password;
      case 'suspicious_activity':
        return Icons.warning;
      case 'system_update':
        return Icons.system_update;
      case 'firewall':
        return Icons.security;
      default:
        return Icons.info;
    }
  }

  Color _getSecurityColor(String severity) {
    switch (severity) {
      case 'Critical':
        return Colors.red;
      case 'Warning':
        return Colors.orange;
      case 'Info':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _refreshSecurityStatus() {
    SuccessSnackBar.show('Security status refreshed');
  }

  void _clearCache() {
    Get.dialog(
      AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear all cached data. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show('Cache cleared successfully');
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _resetSettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('This will reset all settings to default values. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              setState(() {
                _emailNotifications = true;
                _pushNotifications = true;
                _maintenanceMode = false;
                _allowNewRegistrations = true;
                _requireEmailVerification = true;
                _selectedTheme = 'System';
                _selectedLanguage = 'English';
              });
              SuccessSnackBar.show('Settings reset to default');
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
