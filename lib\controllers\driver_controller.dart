import 'dart:async';
import 'dart:math';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../services/demo_data_service.dart';
import 'user_controller.dart';
import 'order_controller.dart';

class DriverController extends GetxController {
  // Observable variables
  final RxList<OrderModel> _availableOrders = <OrderModel>[].obs;
  final RxList<OrderModel> _assignedOrders = <OrderModel>[].obs;
  final Rx<OrderModel?> _currentDelivery = Rx<OrderModel?>(null);
  final RxBool _isOnline = false.obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<DriverLocation> _currentLocation = DriverLocation.empty().obs;
  final Rx<DriverStats> _driverStats = DriverStats.empty().obs;
  final RxBool _isAvailableForDelivery = true.obs;

  // Dependencies
  final UserController _userController = Get.find<UserController>();
  final OrderController _orderController = Get.find<OrderController>();

  // Getters
  List<OrderModel> get availableOrders => _availableOrders;
  List<OrderModel> get assignedOrders => _assignedOrders;
  OrderModel? get currentDelivery => _currentDelivery.value;
  bool get isOnline => _isOnline.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  DriverLocation get currentLocation => _currentLocation.value;
  DriverStats get driverStats => _driverStats.value;
  bool get isAvailableForDelivery => _isAvailableForDelivery.value;

  bool get isDriver => _userController.currentUser?.role == UserRole.driver;
  bool get canAccessDriverPanel => isDriver;

  @override
  void onInit() {
    super.onInit();
    if (canAccessDriverPanel) {
      _loadDriverData();
      _setupLocationTracking();
    }
    _setupOrderListener();
  }

  // Driver status management
  Future<void> goOnline() async {
    if (!canAccessDriverPanel) {
      _errorMessage.value = 'Access denied: Driver privileges required';
      return;
    }

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _isOnline.value = true;
      _isAvailableForDelivery.value = true;

      await _loadAvailableOrders();
      await _saveDriverStatusToStorage();
    } catch (e) {
      _errorMessage.value = 'Failed to go online: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> goOffline() async {
    if (!canAccessDriverPanel) return;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Check if driver has active deliveries
      if (_currentDelivery.value != null) {
        _errorMessage.value = 'Cannot go offline with active delivery';
        return;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _isOnline.value = false;
      _isAvailableForDelivery.value = false;
      _availableOrders.clear();

      await _saveDriverStatusToStorage();
    } catch (e) {
      _errorMessage.value = 'Failed to go offline: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  void setAvailabilityStatus(bool isAvailable) {
    _isAvailableForDelivery.value = isAvailable;
    if (!isAvailable) {
      _availableOrders.clear();
    } else if (_isOnline.value) {
      _loadAvailableOrders();
    }
  }

  // Order management
  Future<void> _loadAvailableOrders() async {
    if (!_isOnline.value || !_isAvailableForDelivery.value) return;

    try {
      // Get orders that are ready for pickup and not assigned to any driver
      final readyOrders = _orderController.orders
          .where((order) => order.status == OrderStatus.ready && order.driverId == null)
          .toList();

      _availableOrders.clear();
      _availableOrders.addAll(readyOrders);
    } catch (e) {
      _errorMessage.value = 'Failed to load available orders: ${e.toString()}';
    }
  }

  Future<bool> acceptOrder(String orderId) async {
    if (!canAccessDriverPanel || !_isOnline.value) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final order = _availableOrders.firstWhereOrNull((o) => o.id == orderId);
      if (order == null) {
        _errorMessage.value = 'Order not found';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Update order with driver assignment
      final updatedOrder = order.copyWith(
        driverId: _userController.currentUser!.id,
        status: OrderStatus.pickedUp,
      );

      // Update in order controller
      await _orderController.updateOrderStatus(orderId, OrderStatus.pickedUp);

      // Move to assigned orders
      _availableOrders.removeWhere((o) => o.id == orderId);
      _assignedOrders.add(updatedOrder);
      _currentDelivery.value = updatedOrder;

      // Update availability
      _isAvailableForDelivery.value = false;

      _calculateDriverStats();
      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to accept order: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> completeDelivery(String orderId) async {
    if (!canAccessDriverPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final order = _assignedOrders.firstWhereOrNull((o) => o.id == orderId);
      if (order == null) {
        _errorMessage.value = 'Order not found';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Update order status to delivered
      await _orderController.updateOrderStatus(orderId, OrderStatus.delivered);

      // Remove from assigned orders
      _assignedOrders.removeWhere((o) => o.id == orderId);

      // Clear current delivery if it's the same order
      if (_currentDelivery.value?.id == orderId) {
        _currentDelivery.value = null;
        _isAvailableForDelivery.value = true;
      }

      _calculateDriverStats();

      // Load new available orders
      if (_isOnline.value && _isAvailableForDelivery.value) {
        await _loadAvailableOrders();
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to complete delivery: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> reportIssue(String orderId, String issue) async {
    if (!canAccessDriverPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, this would report the issue to support
      // For now, we'll just log it

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to report issue: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Location tracking
  void _setupLocationTracking() {
    // Simulate location updates
    if (canAccessDriverPanel) {
      _currentLocation.value = DriverLocation(
        latitude: 37.7749 + (Random().nextDouble() - 0.5) * 0.01,
        longitude: -122.4194 + (Random().nextDouble() - 0.5) * 0.01,
        timestamp: DateTime.now(),
      );

      // Update location every 30 seconds when online
      ever(_isOnline, (isOnline) {
        if (isOnline) {
          _startLocationUpdates();
        }
      });
    }
  }

  void _startLocationUpdates() {
    // In a real app, this would use GPS/location services
    // For demo, we'll simulate location changes
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_isOnline.value) {
        timer.cancel();
        return;
      }

      _currentLocation.value = DriverLocation(
        latitude: _currentLocation.value.latitude + (Random().nextDouble() - 0.5) * 0.001,
        longitude: _currentLocation.value.longitude + (Random().nextDouble() - 0.5) * 0.001,
        timestamp: DateTime.now(),
      );
    });
  }

  // Earnings and statistics
  Future<void> _calculateDriverStats() async {
    try {
      // Try to get driver stats from demo data service
      final driverId = _userController.currentUser?.id;
      if (driverId != null) {
        await DemoDataService.instance.loadDemoData();
        final statsMap = DemoDataService.instance.getDriverStats(driverId);

        _driverStats.value = DriverStats(
          todayDeliveries: statsMap['todayDeliveries'] ?? 0,
          weekDeliveries: statsMap['weekDeliveries'] ?? 0,
          monthDeliveries: statsMap['monthDeliveries'] ?? 0,
          totalDeliveries: statsMap['totalDeliveries'] ?? 0,
          todayEarnings: statsMap['todayEarnings'] ?? 0.0,
          weekEarnings: statsMap['weekEarnings'] ?? 0.0,
          monthEarnings: statsMap['monthEarnings'] ?? 0.0,
          averageDeliveryTime: statsMap['averageDeliveryTime'] ?? 0.0,
          rating: statsMap['rating'] ?? 4.8,
        );
        return;
      }
    } catch (e) {
      // Fallback to calculating from order controller data
    }

    // Fallback calculation
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
    final startOfMonth = DateTime(today.year, today.month, 1);

    // Get completed deliveries
    final allCompletedOrders = _orderController.orders
        .where((order) => order.status == OrderStatus.delivered && order.driverId == _userController.currentUser?.id)
        .toList();

    final todayOrders = allCompletedOrders.where((order) => order.deliveredAt?.isAfter(startOfDay) ?? false).toList();
    final weekOrders = allCompletedOrders.where((order) => order.deliveredAt?.isAfter(startOfWeek) ?? false).toList();
    final monthOrders = allCompletedOrders.where((order) => order.deliveredAt?.isAfter(startOfMonth) ?? false).toList();

    // Calculate earnings (delivery fee + tip)
    final todayEarnings = todayOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);
    final weekEarnings = weekOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);
    final monthEarnings = monthOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);

    _driverStats.value = DriverStats(
      todayDeliveries: todayOrders.length,
      weekDeliveries: weekOrders.length,
      monthDeliveries: monthOrders.length,
      totalDeliveries: allCompletedOrders.length,
      todayEarnings: todayEarnings,
      weekEarnings: weekEarnings,
      monthEarnings: monthEarnings,
      averageDeliveryTime: _calculateAverageDeliveryTime(allCompletedOrders),
      rating: 4.8, // Demo rating
    );
  }

  double _calculateAverageDeliveryTime(List<OrderModel> orders) {
    if (orders.isEmpty) return 0.0;

    final totalMinutes = orders.fold(0.0, (sum, order) {
      if (order.deliveredAt != null) {
        final deliveryTime = order.deliveredAt!.difference(order.placedAt).inMinutes;
        return sum + deliveryTime;
      }
      return sum;
    });

    return totalMinutes / orders.length;
  }

  // Navigation and directions
  String getDirectionsToRestaurant(OrderModel order) {
    // In a real app, this would integrate with maps API
    return 'Navigate to restaurant for order ${order.id}';
  }

  String getDirectionsToCustomer(OrderModel order) {
    // In a real app, this would integrate with maps API
    return 'Navigate to customer for order ${order.id}';
  }

  double calculateDistanceToRestaurant(OrderModel order) {
    // In a real app, this would calculate actual distance
    return Random().nextDouble() * 5 + 1; // 1-6 km
  }

  double calculateDistanceToCustomer(OrderModel order) {
    // In a real app, this would calculate actual distance
    return Random().nextDouble() * 3 + 0.5; // 0.5-3.5 km
  }

  // Data persistence
  Future<void> _loadDriverData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isOnline = prefs.getBool('driver_online_${_userController.currentUser?.id}') ?? false;

      if (isOnline) {
        _isOnline.value = true;
        await _loadAvailableOrders();
      }

      _calculateDriverStats();
    } catch (e) {
      _errorMessage.value = 'Error loading driver data: ${e.toString()}';
    }
  }

  Future<void> _saveDriverStatusToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('driver_online_${_userController.currentUser?.id}', _isOnline.value);
    } catch (e) {
      _errorMessage.value = 'Error saving driver status: ${e.toString()}';
    }
  }

  // Private methods
  void _setupOrderListener() {
    // Listen for new orders that become available
    // Note: In a real app, you would listen to the actual RxList
    // For now, we'll use a different approach
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_isOnline.value) return;

      if (_isAvailableForDelivery.value) {
        _loadAvailableOrders();
      }
      _calculateDriverStats();
    });
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  String formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String formatDistance(double distance) {
    return '${distance.toStringAsFixed(1)} km';
  }

  String formatDuration(double minutes) {
    if (minutes < 60) {
      return '${minutes.round()} min';
    } else {
      final hours = minutes / 60;
      return '${hours.toStringAsFixed(1)} hr';
    }
  }

  String getOrderStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.delivered:
        return 'Delivered';
      default:
        return status.toString().split('.').last;
    }
  }
}

class DriverLocation {
  final double latitude;
  final double longitude;
  final DateTime timestamp;

  DriverLocation({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
  });

  factory DriverLocation.empty() {
    return DriverLocation(
      latitude: 0.0,
      longitude: 0.0,
      timestamp: DateTime.now(),
    );
  }
}

class DriverStats {
  final int todayDeliveries;
  final int weekDeliveries;
  final int monthDeliveries;
  final int totalDeliveries;
  final double todayEarnings;
  final double weekEarnings;
  final double monthEarnings;
  final double averageDeliveryTime;
  final double rating;

  DriverStats({
    required this.todayDeliveries,
    required this.weekDeliveries,
    required this.monthDeliveries,
    required this.totalDeliveries,
    required this.todayEarnings,
    required this.weekEarnings,
    required this.monthEarnings,
    required this.averageDeliveryTime,
    required this.rating,
  });

  factory DriverStats.empty() {
    return DriverStats(
      todayDeliveries: 0,
      weekDeliveries: 0,
      monthDeliveries: 0,
      totalDeliveries: 0,
      todayEarnings: 0.0,
      weekEarnings: 0.0,
      monthEarnings: 0.0,
      averageDeliveryTime: 0.0,
      rating: 0.0,
    );
  }
}
