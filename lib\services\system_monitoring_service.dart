import 'dart:async';
import 'dart:math';
import 'package:get/get.dart';

class SystemMonitoringService extends GetxService {
  // System health metrics
  final RxDouble cpuUsage = 0.0.obs;
  final RxDouble memoryUsage = 0.0.obs;
  final RxDouble diskUsage = 0.0.obs;
  final RxInt activeConnections = 0.obs;
  final RxInt apiRequestsPerMinute = 0.obs;
  final RxDouble responseTime = 0.0.obs;
  final RxBool systemHealthy = true.obs;
  
  // Performance metrics
  final RxList<Map<String, dynamic>> performanceHistory = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> errorLogs = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> systemAlerts = <Map<String, dynamic>>[].obs;
  
  Timer? _monitoringTimer;
  final Random _random = Random();
  
  @override
  void onInit() {
    super.onInit();
    _initializeMonitoring();
    _generateDemoData();
  }
  
  @override
  void onClose() {
    _monitoringTimer?.cancel();
    super.onClose();
  }
  
  void _initializeMonitoring() {
    // Start real-time monitoring
    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _updateSystemMetrics();
    });
  }
  
  void _updateSystemMetrics() {
    // Simulate real system metrics (in production, these would be real metrics)
    cpuUsage.value = 20 + _random.nextDouble() * 60; // 20-80%
    memoryUsage.value = 30 + _random.nextDouble() * 50; // 30-80%
    diskUsage.value = 45 + _random.nextDouble() * 30; // 45-75%
    activeConnections.value = 50 + _random.nextInt(200); // 50-250
    apiRequestsPerMinute.value = 100 + _random.nextInt(500); // 100-600
    responseTime.value = 50 + _random.nextDouble() * 200; // 50-250ms
    
    // Update system health status
    systemHealthy.value = cpuUsage.value < 85 && 
                         memoryUsage.value < 90 && 
                         responseTime.value < 300;
    
    // Add to performance history
    _addPerformanceRecord();
    
    // Check for alerts
    _checkSystemAlerts();
  }
  
  void _addPerformanceRecord() {
    final record = {
      'timestamp': DateTime.now(),
      'cpu': cpuUsage.value,
      'memory': memoryUsage.value,
      'disk': diskUsage.value,
      'connections': activeConnections.value,
      'apiRequests': apiRequestsPerMinute.value,
      'responseTime': responseTime.value,
    };
    
    performanceHistory.add(record);
    
    // Keep only last 100 records
    if (performanceHistory.length > 100) {
      performanceHistory.removeAt(0);
    }
  }
  
  void _checkSystemAlerts() {
    final now = DateTime.now();
    
    // CPU usage alert
    if (cpuUsage.value > 80) {
      _addAlert('High CPU Usage', 'CPU usage is at ${cpuUsage.value.toStringAsFixed(1)}%', 'warning', now);
    }
    
    // Memory usage alert
    if (memoryUsage.value > 85) {
      _addAlert('High Memory Usage', 'Memory usage is at ${memoryUsage.value.toStringAsFixed(1)}%', 'warning', now);
    }
    
    // Response time alert
    if (responseTime.value > 250) {
      _addAlert('Slow Response Time', 'Average response time is ${responseTime.value.toStringAsFixed(0)}ms', 'warning', now);
    }
  }
  
  void _addAlert(String title, String message, String severity, DateTime timestamp) {
    // Check if similar alert already exists in last 5 minutes
    final recentAlerts = systemAlerts.where((alert) {
      final alertTime = alert['timestamp'] as DateTime;
      return alert['title'] == title && 
             timestamp.difference(alertTime).inMinutes < 5;
    });
    
    if (recentAlerts.isEmpty) {
      systemAlerts.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'title': title,
        'message': message,
        'severity': severity,
        'timestamp': timestamp,
        'acknowledged': false,
      });
      
      // Keep only last 50 alerts
      if (systemAlerts.length > 50) {
        systemAlerts.removeAt(0);
      }
    }
  }
  
  void _generateDemoData() {
    // Generate demo error logs
    final demoErrors = [
      {
        'id': '1',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
        'level': 'ERROR',
        'message': 'Database connection timeout',
        'source': 'DatabaseService',
        'details': 'Connection to primary database failed after 30 seconds',
      },
      {
        'id': '2',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'level': 'WARNING',
        'message': 'High memory usage detected',
        'source': 'SystemMonitor',
        'details': 'Memory usage exceeded 85% threshold',
      },
      {
        'id': '3',
        'timestamp': DateTime.now().subtract(const Duration(hours: 6)),
        'level': 'INFO',
        'message': 'System backup completed',
        'source': 'BackupService',
        'details': 'Daily backup completed successfully',
      },
    ];
    
    errorLogs.addAll(demoErrors);
    
    // Generate demo alerts
    final demoAlerts = [
      {
        'id': 'alert_1',
        'title': 'Disk Space Warning',
        'message': 'Disk usage is approaching 80%',
        'severity': 'warning',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
        'acknowledged': false,
      },
      {
        'id': 'alert_2',
        'title': 'Security Update Available',
        'message': 'New security patches are available for installation',
        'severity': 'info',
        'timestamp': DateTime.now().subtract(const Duration(hours: 4)),
        'acknowledged': true,
      },
    ];
    
    systemAlerts.addAll(demoAlerts);
  }
  
  // Public methods for external access
  Map<String, dynamic> getSystemHealth() {
    return {
      'healthy': systemHealthy.value,
      'cpu': cpuUsage.value,
      'memory': memoryUsage.value,
      'disk': diskUsage.value,
      'connections': activeConnections.value,
      'apiRequests': apiRequestsPerMinute.value,
      'responseTime': responseTime.value,
    };
  }
  
  List<Map<String, dynamic>> getRecentPerformance(int hours) {
    final cutoff = DateTime.now().subtract(Duration(hours: hours));
    return performanceHistory.where((record) {
      final timestamp = record['timestamp'] as DateTime;
      return timestamp.isAfter(cutoff);
    }).toList();
  }
  
  List<Map<String, dynamic>> getUnacknowledgedAlerts() {
    return systemAlerts.where((alert) => !alert['acknowledged']).toList();
  }
  
  void acknowledgeAlert(String alertId) {
    final alertIndex = systemAlerts.indexWhere((alert) => alert['id'] == alertId);
    if (alertIndex != -1) {
      systemAlerts[alertIndex]['acknowledged'] = true;
      systemAlerts.refresh();
    }
  }
  
  void clearOldLogs(int daysToKeep) {
    final cutoff = DateTime.now().subtract(Duration(days: daysToKeep));
    errorLogs.removeWhere((log) {
      final timestamp = log['timestamp'] as DateTime;
      return timestamp.isBefore(cutoff);
    });
  }
  
  // Restart monitoring (useful for testing)
  void restartMonitoring() {
    _monitoringTimer?.cancel();
    _initializeMonitoring();
  }
}
