import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/audit_log.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../core/app_constants.dart';
import 'error_service.dart';
import 'audit_service.dart';

/// Real-time service for WebSocket connections and live updates
class RealtimeService extends GetxService {
  static RealtimeService get instance => Get.find<RealtimeService>();

  // WebSocket connection
  WebSocketChannel? _channel;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;

  // Connection state
  final RxBool _isConnected = false.obs;
  final RxBool _isConnecting = false.obs;
  final RxString _connectionStatus = 'Disconnected'.obs;
  final RxInt _reconnectAttempts = 0.obs;

  // Real-time data streams
  final RxList<OrderModel> _liveOrders = <OrderModel>[].obs;
  final RxList<Map<String, dynamic>> _liveNotifications = <Map<String, dynamic>>[].obs;
  final RxMap<String, dynamic> _driverLocations = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> _restaurantStatus = <String, dynamic>{}.obs;

  // Stream controllers for different event types
  final StreamController<OrderModel> _orderUpdatesController = StreamController<OrderModel>.broadcast();
  final StreamController<Map<String, dynamic>> _notificationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _locationController = StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _statusController = StreamController<Map<String, dynamic>>.broadcast();

  // Services
  late final ErrorService _errorService;
  late final AuditService _auditService;

  // Configuration
  static const int maxReconnectAttempts = 5;
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration reconnectDelay = Duration(seconds: 5);

  // Getters
  bool get isConnected => _isConnected.value;
  bool get isConnecting => _isConnecting.value;
  String get connectionStatus => _connectionStatus.value;
  int get reconnectAttempts => _reconnectAttempts.value;
  List<OrderModel> get liveOrders => _liveOrders;
  List<Map<String, dynamic>> get liveNotifications => _liveNotifications;
  Map<String, dynamic> get driverLocations => _driverLocations;
  Map<String, dynamic> get restaurantStatus => _restaurantStatus;

  // Event streams
  Stream<OrderModel> get orderUpdates => _orderUpdatesController.stream;
  Stream<Map<String, dynamic>> get notifications => _notificationController.stream;
  Stream<Map<String, dynamic>> get locationUpdates => _locationController.stream;
  Stream<Map<String, dynamic>> get statusUpdates => _statusController.stream;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeRealtime();
  }

  @override
  void onClose() {
    _cleanup();
    super.onClose();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
    _auditService = Get.find<AuditService>();
  }

  void _initializeRealtime() {
    // Auto-connect if enabled
    if (AppConstants.enableRealTimeUpdates) {
      connect();
    }
  }

  /// Connect to WebSocket server
  Future<void> connect({String? userId}) async {
    if (_isConnecting.value || _isConnected.value) return;

    try {
      _isConnecting.value = true;
      _connectionStatus.value = 'Connecting...';

      // Build WebSocket URL with authentication
      final wsUrl = _buildWebSocketUrl(userId);

      // Create WebSocket connection
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));

      // Listen for connection establishment
      await _channel!.ready;

      _isConnected.value = true;
      _isConnecting.value = false;
      _connectionStatus.value = 'Connected';
      _reconnectAttempts.value = 0;

      // Set up message handling
      _setupMessageHandling();

      // Start heartbeat
      _startHeartbeat();

      // Send authentication message
      _sendAuthentication(userId);

      await _auditService.logAction(
        action: AuditAction.systemConfig,
        entityType: 'RealtimeService',
        reason: 'WebSocket connection established',
        severity: AuditSeverity.info,
        entityId: userId!,
      );
    } catch (e) {
      _handleConnectionError(e);
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    try {
      _isConnecting.value = false;
      _connectionStatus.value = 'Disconnecting...';

      _cleanup();

      _isConnected.value = false;
      _connectionStatus.value = 'Disconnected';

      await _auditService.logAction(
        action: AuditAction.systemConfig,
        entityType: 'RealtimeService',
        reason: 'WebSocket connection closed',
        severity: AuditSeverity.info,
        
      );
    } catch (e) {
      _errorService.handleAppError(e, context: 'RealtimeService.disconnect');
    }
  }

  /// Send message to WebSocket server
  void sendMessage(String type, Map<String, dynamic> data) {
    if (!_isConnected.value || _channel == null) {
      _errorService.handleAppError(
        'Cannot send message: WebSocket not connected',
        context: 'RealtimeService.sendMessage',
      );
      return;
    }

    try {
      final message = {
        'type': type,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _channel!.sink.add(jsonEncode(message));
    } catch (e) {
      _errorService.handleAppError(e, context: 'RealtimeService.sendMessage');
    }
  }

  /// Subscribe to order updates for a specific order
  void subscribeToOrder(String orderId) {
    sendMessage('subscribe_order', {'orderId': orderId});
  }

  /// Unsubscribe from order updates
  void unsubscribeFromOrder(String orderId) {
    sendMessage('unsubscribe_order', {'orderId': orderId});
  }

  /// Subscribe to driver location updates
  void subscribeToDriverLocation(String driverId) {
    sendMessage('subscribe_driver', {'driverId': driverId});
  }

  /// Update driver location (for drivers)
  void updateDriverLocation(String driverId, double latitude, double longitude) {
    sendMessage('driver_location', {
      'driverId': driverId,
      'latitude': latitude,
      'longitude': longitude,
    });
  }

  /// Update restaurant status
  void updateRestaurantStatus(String restaurantId, Map<String, dynamic> status) {
    sendMessage('restaurant_status', {
      'restaurantId': restaurantId,
      'status': status,
    });
  }

  /// Send order status update
  void sendOrderUpdate(String orderId, OrderStatus status, {String? message}) {
    sendMessage('order_update', {
      'orderId': orderId,
      'status': status.name,
      'message': message,
    });
  }

  String _buildWebSocketUrl(String? userId) {
    // In production, this would be your actual WebSocket server URL
    final baseUrl = EnvironmentConfig.isProduction ? 'wss://ws.restauranthub.com' : 'ws://localhost:8080';

    final params = <String, String>{};
    if (userId != null) params['userId'] = userId;

    final queryString = params.entries.map((e) => '${e.key}=${Uri.encodeComponent(e.value)}').join('&');

    return queryString.isEmpty ? baseUrl : '$baseUrl?$queryString';
  }

  void _setupMessageHandling() {
    _channel!.stream.listen(
      _handleMessage,
      onError: _handleConnectionError,
      onDone: _handleConnectionClosed,
    );
  }

  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String);
      final type = data['type'] as String;
      final payload = data['data'] as Map<String, dynamic>;

      switch (type) {
        case 'order_update':
          _handleOrderUpdate(payload);
          break;
        case 'notification':
          _handleNotification(payload);
          break;
        case 'driver_location':
          _handleDriverLocation(payload);
          break;
        case 'restaurant_status':
          _handleRestaurantStatus(payload);
          break;
        case 'heartbeat':
          _handleHeartbeat(payload);
          break;
        default:
          // Handle unknown message types
          break;
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'RealtimeService.handleMessage');
    }
  }

  void _handleOrderUpdate(Map<String, dynamic> data) {
    try {
      final order = OrderModel.fromJson(data);

      // Update local order list
      final existingIndex = _liveOrders.indexWhere((o) => o.id == order.id);
      if (existingIndex != -1) {
        _liveOrders[existingIndex] = order;
      } else {
        _liveOrders.add(order);
      }

      // Emit to stream
      _orderUpdatesController.add(order);
    } catch (e) {
      _errorService.handleAppError(e, context: 'RealtimeService.handleOrderUpdate');
    }
  }

  void _handleNotification(Map<String, dynamic> data) {
    _liveNotifications.insert(0, data);

    // Keep only recent notifications
    if (_liveNotifications.length > 100) {
      _liveNotifications.removeRange(100, _liveNotifications.length);
    }

    _notificationController.add(data);
  }

  void _handleDriverLocation(Map<String, dynamic> data) {
    final driverId = data['driverId'] as String;
    _driverLocations[driverId] = data;
    _locationController.add(data);
  }

  void _handleRestaurantStatus(Map<String, dynamic> data) {
    final restaurantId = data['restaurantId'] as String;
    _restaurantStatus[restaurantId] = data['status'];
    _statusController.add(data);
  }

  void _handleHeartbeat(Map<String, dynamic> data) {
    // Respond to server heartbeat
    sendMessage('heartbeat_response', {'timestamp': DateTime.now().toIso8601String()});
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (_isConnected.value) {
        sendMessage('heartbeat', {'timestamp': DateTime.now().toIso8601String()});
      } else {
        timer.cancel();
      }
    });
  }

  void _sendAuthentication(String? userId) {
    if (userId != null) {
      sendMessage('authenticate', {'userId': userId});
    }
  }

  void _handleConnectionError(dynamic error) {
    _isConnected.value = false;
    _isConnecting.value = false;
    _connectionStatus.value = 'Connection Error';

    _errorService.handleAppError(error, context: 'RealtimeService.connectionError');

    // Attempt reconnection
    _attemptReconnection();
  }

  void _handleConnectionClosed() {
    _isConnected.value = false;
    _connectionStatus.value = 'Connection Closed';

    // Attempt reconnection if not manually disconnected
    if (_reconnectAttempts.value < maxReconnectAttempts) {
      _attemptReconnection();
    }
  }

  void _attemptReconnection() {
    if (_reconnectAttempts.value >= maxReconnectAttempts) {
      _connectionStatus.value = 'Connection Failed';
      return;
    }

    _reconnectAttempts.value++;
    _connectionStatus.value = 'Reconnecting... (${_reconnectAttempts.value}/$maxReconnectAttempts)';

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      connect();
    });
  }

  void _cleanup() {
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();

    _channel?.sink.close(status.normalClosure);
    _channel = null;
  }
}
