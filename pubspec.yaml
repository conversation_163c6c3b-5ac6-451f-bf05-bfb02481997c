name: restauranthub
description: A new Flutter project.
version: 1.0.0+1
environment: 
  sdk: ^3.6.0
dependencies:
  flutter:
    sdk: flutter
  google_fonts: ^6.2.1
  get: ^4.6.6
  http: ^1.1.0
  shared_preferences: ^2.2.2
  crypto: ^3.0.3
  image_picker: ^1.0.4
  web_socket_channel: ^3.0.3
  socket_io_client: ^3.1.2
  firebase_core: ^3.15.1
  firebase_messaging: ^15.2.9
  flutter_local_notifications: ^19.3.0
  geolocator: ^14.0.2
  geocoding: ^4.0.0
  permission_handler: ^12.0.1
  cached_network_image: ^3.4.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  firebase_crashlytics: ^4.3.9
  logger: ^2.6.0
dev_dependencies: 
  build_runner: ^2.5.4
  flutter_lints: ^6.0.0
  mockito: ^5.4.6
  test: ^1.25.15
flutter:
  uses-material-design: true
  assets:
    - demo_data.json