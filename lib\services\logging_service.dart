import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/app_constants.dart';

/// Comprehensive logging and crash reporting service
class LoggingService extends GetxService {
  static LoggingService get instance => Get.find<LoggingService>();

  // Logger instance
  late final Logger _logger;

  // Crashlytics instance
  FirebaseCrashlytics? _crashlytics;

  // Log configuration
  final RxBool _isInitialized = false.obs;
  final RxBool _crashReportingEnabled = true.obs;
  final RxBool _analyticsEnabled = true.obs;
  final RxInt _logLevel = Level.info.index.obs;

  // Log statistics
  final RxInt _totalLogs = 0.obs;
  final RxInt _errorCount = 0.obs;
  final RxInt _warningCount = 0.obs;
  final RxInt _crashCount = 0.obs;

  // Log buffer for offline scenarios
  final List<LogEntry> _logBuffer = [];
  static const int maxLogBufferSize = 1000;

  // Getters
  bool get isInitialized => _isInitialized.value;
  bool get crashReportingEnabled => _crashReportingEnabled.value;
  bool get analyticsEnabled => _analyticsEnabled.value;
  Level get logLevel => Level.values[_logLevel.value];
  int get totalLogs => _totalLogs.value;
  int get errorCount => _errorCount.value;
  int get warningCount => _warningCount.value;
  int get crashCount => _crashCount.value;

  @override
  void onInit() {
    super.onInit();
    _initializeLogging();
  }

  Future<void> _initializeLogging() async {
    try {
      // Initialize logger with custom configuration
      _logger = Logger(
        filter: ProductionFilter(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: !kReleaseMode,
          printEmojis: !kReleaseMode,
          printTime: true,
        ),
        output: MultiOutput([
          ConsoleOutput(),
          if (EnvironmentConfig.enableLogging) FileOutput(),
        ]),
      );

      // Initialize Firebase Crashlytics
      await _initializeCrashlytics();

      // Load settings
      await _loadSettings();

      // Set up global error handling
      _setupGlobalErrorHandling();

      _isInitialized.value = true;

      logInfo('LoggingService initialized successfully');
    } catch (e) {
      // Fallback logging if initialization fails
      if (kDebugMode) {
        print('LoggingService initialization failed: $e');
      }
    }
  }

  Future<void> _initializeCrashlytics() async {
    try {
      if (!kDebugMode && _crashReportingEnabled.value) {
        _crashlytics = FirebaseCrashlytics.instance;

        // Enable crash collection
        await _crashlytics!.setCrashlyticsCollectionEnabled(true);

        // Set user identifier
        await _setUserIdentifier();

        logInfo('Firebase Crashlytics initialized');
      }
    } catch (e) {
      logError('Failed to initialize Crashlytics', error: e);
    }
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _crashReportingEnabled.value = prefs.getBool('crash_reporting_enabled') ?? true;
      _analyticsEnabled.value = prefs.getBool('analytics_enabled') ?? true;
      _logLevel.value = prefs.getInt('log_level') ?? Level.info.index;
    } catch (e) {
      logError('Failed to load logging settings', error: e);
    }
  }

  void _setupGlobalErrorHandling() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      logError(
        'Flutter Error: ${details.exception}',
        error: details.exception,
        stackTrace: details.stack,
        context: details.context?.toString(),
      );

      // Report to Crashlytics
      _crashlytics?.recordFlutterFatalError(details);
    };

    // Handle async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      logError(
        'Async Error: $error',
        error: error,
        stackTrace: stack,
      );

      // Report to Crashlytics
      _crashlytics?.recordError(error, stack, fatal: true);

      return true;
    };
  }

  // Logging methods
  void logDebug(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
  }) {
    if (_shouldLog(Level.debug)) {
      _log(Level.debug, message, error: error, stackTrace: stackTrace, context: context, data: data);
    }
  }

  void logInfo(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
  }) {
    if (_shouldLog(Level.info)) {
      _log(Level.info, message, error: error, stackTrace: stackTrace, context: context, data: data);
    }
  }

  void logWarning(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
  }) {
    if (_shouldLog(Level.warning)) {
      _log(Level.warning, message, error: error, stackTrace: stackTrace, context: context, data: data);
      _warningCount.value++;
    }
  }

  void logError(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
    bool fatal = false,
  }) {
    if (_shouldLog(Level.error)) {
      _log(Level.error, message, error: error, stackTrace: stackTrace, context: context, data: data);
      _errorCount.value++;

      // Report to Crashlytics
      if (_crashlytics != null && error != null) {
        _crashlytics!.recordError(error, stackTrace, fatal: fatal);
      }
    }
  }

  void logFatal(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
  }) {
    _log(Level.fatal, message, error: error, stackTrace: stackTrace, context: context, data: data);
    _errorCount.value++;
    _crashCount.value++;

    // Always report fatal errors to Crashlytics
    if (_crashlytics != null && error != null) {
      _crashlytics!.recordError(error, stackTrace, fatal: true);
    }
  }

  void _log(
    Level level,
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? data,
  }) {
    try {
      final logEntry = LogEntry(
        level: level,
        message: message,
        error: error,
        stackTrace: stackTrace,
        context: context,
        data: data,
        timestamp: DateTime.now(),
      );

      // Add to buffer
      _addToBuffer(logEntry);

      // Log using logger
      if (_isInitialized.value) {
        final logMessage = _formatLogMessage(logEntry);

        switch (level) {
          case Level.debug:
            _logger.d(logMessage, error: error, stackTrace: stackTrace);
            break;
          case Level.info:
            _logger.i(logMessage, error: error, stackTrace: stackTrace);
            break;
          case Level.warning:
            _logger.w(logMessage, error: error, stackTrace: stackTrace);
            break;
          case Level.error:
            _logger.e(logMessage, error: error, stackTrace: stackTrace);
            break;
          case Level.fatal:
            _logger.f(logMessage, error: error, stackTrace: stackTrace);
            break;
          default:
            _logger.i(logMessage, error: error, stackTrace: stackTrace);
        }
      } else {
        // Fallback to print if logger not initialized
        if (kDebugMode) {
          print('${level.name.toUpperCase()}: $message');
          if (error != null) print('Error: $error');
        }
      }

      _totalLogs.value++;
    } catch (e) {
      // Fallback logging
      if (kDebugMode) {
        print('Logging failed: $e');
        print('Original message: $message');
      }
    }
  }

  String _formatLogMessage(LogEntry entry) {
    final buffer = StringBuffer();

    if (entry.context != null) {
      buffer.write('[${entry.context}] ');
    }

    buffer.write(entry.message);

    if (entry.data != null && entry.data!.isNotEmpty) {
      buffer.write(' | Data: ${entry.data}');
    }

    return buffer.toString();
  }

  void _addToBuffer(LogEntry entry) {
    _logBuffer.add(entry);

    // Keep buffer size manageable
    if (_logBuffer.length > maxLogBufferSize) {
      _logBuffer.removeAt(0);
    }
  }

  bool _shouldLog(Level level) {
    return level.index >= _logLevel.value;
  }

  // User identification for crash reporting
  Future<void> _setUserIdentifier() async {
    try {
      // This would typically come from authentication service
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      await _crashlytics?.setUserIdentifier(userId);
    } catch (e) {
      logError('Failed to set user identifier', error: e);
    }
  }

  // Custom crash reporting
  Future<void> reportCrash(
    String message,
    Object error,
    StackTrace stackTrace, {
    Map<String, dynamic>? customData,
    bool fatal = true,
  }) async {
    try {
      logFatal(message, error: error, stackTrace: stackTrace, data: customData);

      if (_crashlytics != null) {
        // Set custom keys
        if (customData != null) {
          for (final entry in customData.entries) {
            await _crashlytics!.setCustomKey(entry.key, entry.value.toString());
          }
        }

        // Record the crash
        await _crashlytics!.recordError(error, stackTrace, fatal: fatal);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to report crash: $e');
      }
    }
  }

  // Settings management
  Future<void> setCrashReportingEnabled(bool enabled) async {
    try {
      _crashReportingEnabled.value = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('crash_reporting_enabled', enabled);

      if (_crashlytics != null) {
        await _crashlytics!.setCrashlyticsCollectionEnabled(enabled);
      }

      logInfo('Crash reporting ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      logError('Failed to update crash reporting setting', error: e);
    }
  }

  Future<void> setAnalyticsEnabled(bool enabled) async {
    try {
      _analyticsEnabled.value = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('analytics_enabled', enabled);

      logInfo('Analytics ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      logError('Failed to update analytics setting', error: e);
    }
  }

  Future<void> setLogLevel(Level level) async {
    try {
      _logLevel.value = level.index;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('log_level', level.index);

      logInfo('Log level set to ${level.name}');
    } catch (e) {
      logError('Failed to update log level', error: e);
    }
  }

  // Log retrieval
  List<LogEntry> getRecentLogs({int? limit}) {
    final logs = List<LogEntry>.from(_logBuffer);
    if (limit != null && logs.length > limit) {
      return logs.sublist(logs.length - limit);
    }
    return logs;
  }

  List<LogEntry> getLogsByLevel(Level level) {
    return _logBuffer.where((log) => log.level == level).toList();
  }

  // Statistics
  Map<String, dynamic> getLoggingStatistics() {
    return {
      'totalLogs': _totalLogs.value,
      'errorCount': _errorCount.value,
      'warningCount': _warningCount.value,
      'crashCount': _crashCount.value,
      'bufferSize': _logBuffer.length,
      'logLevel': logLevel.name,
      'crashReportingEnabled': _crashReportingEnabled.value,
      'analyticsEnabled': _analyticsEnabled.value,
    };
  }

  // Clear logs
  void clearLogs() {
    _logBuffer.clear();
    _totalLogs.value = 0;
    _errorCount.value = 0;
    _warningCount.value = 0;
    _crashCount.value = 0;

    logInfo('Log buffer cleared');
  }
}

/// Log entry model
class LogEntry {
  final Level level;
  final String message;
  final Object? error;
  final StackTrace? stackTrace;
  final String? context;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  LogEntry({
    required this.level,
    required this.message,
    this.error,
    this.stackTrace,
    this.context,
    this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'message': message,
      'error': error?.toString(),
      'stackTrace': stackTrace?.toString(),
      'context': context,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Custom file output for logger
class FileOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // In a real implementation, you would write to a file
    // For now, we'll just use the console output
    for (final line in event.lines) {
      if (kDebugMode) {
        print(line);
      }
    }
  }
}
