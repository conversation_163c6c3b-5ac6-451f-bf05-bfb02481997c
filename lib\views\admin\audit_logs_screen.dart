import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/audit_log.dart';
import '../../services/audit_service.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/empty_state_widget.dart';

class AuditLogsScreen extends StatefulWidget {
  const AuditLogsScreen({super.key});

  @override
  State<AuditLogsScreen> createState() => _AuditLogsScreenState();
}

class _AuditLogsScreenState extends State<AuditLogsScreen> with TickerProviderStateMixin {
  final AuditService _auditService = Get.find<AuditService>();
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  List<AuditLog> _filteredLogs = [];
  AuditSeverity? _selectedSeverity;
  AuditAction? _selectedAction;
  DateTime? _startDate;
  DateTime? _endDate;

  final List<String> _tabs = ['All', 'Critical', 'Errors', 'Warnings', 'Info'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _filteredLogs = _auditService.auditLogs;
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    setState(() {
      switch (_tabController.index) {
        case 0: // All
          _selectedSeverity = null;
          break;
        case 1: // Critical
          _selectedSeverity = AuditSeverity.critical;
          break;
        case 2: // Errors
          _selectedSeverity = AuditSeverity.error;
          break;
        case 3: // Warnings
          _selectedSeverity = AuditSeverity.warning;
          break;
        case 4: // Info
          _selectedSeverity = AuditSeverity.info;
          break;
      }
      _applyFilters();
    });
  }

  void _applyFilters() {
    var logs = _auditService.auditLogs;

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      logs = _auditService.searchLogs(_searchController.text);
    }

    // Apply severity filter
    if (_selectedSeverity != null) {
      logs = logs.where((log) => log.severity == _selectedSeverity).toList();
    }

    // Apply action filter
    if (_selectedAction != null) {
      logs = logs.where((log) => log.action == _selectedAction).toList();
    }

    // Apply date range filter
    if (_startDate != null && _endDate != null) {
      logs = logs
          .where((log) =>
              log.timestamp.isAfter(_startDate!) && log.timestamp.isBefore(_endDate!.add(const Duration(days: 1))))
          .toList();
    }

    setState(() {
      _filteredLogs = logs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Audit Logs',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportLogs,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
          isScrollable: true,
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search audit logs...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _applyFilters();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) => _applyFilters(),
            ),
          ),
          // Logs list
          Expanded(
            child: Obx(() {
              if (_auditService.isLoading) {
                return const LoadingWidget();
              }

              if (_filteredLogs.isEmpty) {
                return const EmptyStateWidget(
                  icon: Icons.history,
                  title: 'No Audit Logs',
                  message: 'No audit logs match your current filters.',
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  await _auditService.refresh();
                  _applyFilters();
                },
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredLogs.length,
                  itemBuilder: (context, index) {
                    final log = _filteredLogs[index];
                    return _buildLogItem(log);
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildLogItem(AuditLog log) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getSeverityColor(log.severity).withOpacity(0.1),
          child: Icon(
            _getActionIcon(log.action),
            color: _getSeverityColor(log.severity),
            size: 20,
          ),
        ),
        title: Text(
          log.action.displayName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(log.reason ?? 'No reason provided'),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.person, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  log.userName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatTimestamp(log.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getSeverityColor(log.severity).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            log.severity.name.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: _getSeverityColor(log.severity),
            ),
          ),
        ),
        onTap: () => _showLogDetails(log),
      ),
    );
  }

  Color _getSeverityColor(AuditSeverity severity) {
    switch (severity) {
      case AuditSeverity.info:
        return Colors.blue;
      case AuditSeverity.warning:
        return Colors.orange;
      case AuditSeverity.error:
        return Colors.red;
      case AuditSeverity.critical:
        return Colors.red[900]!;
    }
  }

  IconData _getActionIcon(AuditAction action) {
    switch (action) {
      case AuditAction.create:
        return Icons.add;
      case AuditAction.update:
        return Icons.edit;
      case AuditAction.delete:
        return Icons.delete;
      case AuditAction.login:
        return Icons.login;
      case AuditAction.logout:
        return Icons.logout;
      case AuditAction.passwordChange:
        return Icons.lock;
      case AuditAction.roleChange:
        return Icons.admin_panel_settings;
      case AuditAction.suspend:
        return Icons.block;
      case AuditAction.activate:
        return Icons.check_circle;
      case AuditAction.approve:
        return Icons.thumb_up;
      case AuditAction.reject:
        return Icons.thumb_down;
      case AuditAction.refund:
        return Icons.money_off;
      case AuditAction.export:
        return Icons.download;
      case AuditAction.import:
        return Icons.upload;
      case AuditAction.systemConfig:
        return Icons.settings;
      case AuditAction.loginFailed:
        return Icons.error;
      case AuditAction.accountLocked:
        return Icons.lock;
      case AuditAction.emailVerified:
        return Icons.verified;
      case AuditAction.passwordResetRequested:
        return Icons.password;
      case AuditAction.passwordReset:
        return Icons.lock_reset;
      case AuditAction.register:
        return Icons.person_add;
      case AuditAction.unknown:
        return Icons.help;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Audit Logs'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Action filter
            DropdownButtonFormField<AuditAction>(
              value: _selectedAction,
              decoration: const InputDecoration(labelText: 'Action'),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Actions')),
                ...AuditAction.values.map((action) => DropdownMenuItem(
                      value: action,
                      child: Text(action.displayName),
                    )),
              ],
              onChanged: (value) => setState(() => _selectedAction = value),
            ),
            const SizedBox(height: 16),
            // Date range
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _startDate ?? DateTime.now(),
                        firstDate: DateTime.now().subtract(const Duration(days: 365)),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() => _startDate = date);
                      }
                    },
                    child: Text(_startDate != null
                        ? 'From: ${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                        : 'Start Date'),
                  ),
                ),
                Expanded(
                  child: TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _endDate ?? DateTime.now(),
                        firstDate: _startDate ?? DateTime.now().subtract(const Duration(days: 365)),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() => _endDate = date);
                      }
                    },
                    child: Text(
                        _endDate != null ? 'To: ${_endDate!.day}/${_endDate!.month}/${_endDate!.year}' : 'End Date'),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedAction = null;
                _startDate = null;
                _endDate = null;
              });
              _applyFilters();
              Navigator.pop(context);
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () {
              _applyFilters();
              Navigator.pop(context);
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showLogDetails(AuditLog log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(log.action.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('User', log.userName),
            _buildDetailRow('Target', '${log.entityType} (${log.entityId})'),
            _buildDetailRow('Details', log.reason ?? 'No reason provided'),
            _buildDetailRow('Timestamp', log.timestamp.toString()),
            _buildDetailRow('Severity', log.severity.name.toUpperCase()),
            if (log.ipAddress != null) _buildDetailRow('IP Address', log.ipAddress!),
            if (log.userAgent != null) _buildDetailRow('User Agent', log.userAgent!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _exportLogs() {
    // TODO: Implement log export functionality
    Get.snackbar(
      'Export',
      'Audit logs export feature coming soon!',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
