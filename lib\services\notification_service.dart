import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/order.dart';
import '../core/app_constants.dart';
import '../core/app_routes.dart';
import 'error_service.dart';
import 'audit_service.dart';

/// Comprehensive notification service for push notifications and local notifications
class NotificationService extends GetxService {
  static NotificationService get instance => Get.find<NotificationService>();

  // Firebase Messaging
  FirebaseMessaging? _firebaseMessaging;
  
  // Local Notifications
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  // Notification state
  final RxBool _isInitialized = false.obs;
  final RxBool _permissionGranted = false.obs;
  final RxString _fcmToken = ''.obs;
  final RxList<Map<String, dynamic>> _notificationHistory = <Map<String, dynamic>>[].obs;
  
  // Notification settings
  final RxBool _orderUpdatesEnabled = true.obs;
  final RxBool _promotionsEnabled = true.obs;
  final RxBool _newRestaurantsEnabled = true.obs;
  final RxBool _deliveryUpdatesEnabled = true.obs;
  final RxBool _paymentNotificationsEnabled = true.obs;
  final RxBool _soundEnabled = true.obs;
  final RxBool _vibrationEnabled = true.obs;
  
  // Services
  late final ErrorService _errorService;
  late final AuditService _auditService;
  
  // Notification channels
  static const String orderChannel = 'restaurant_hub_orders';
  static const String promotionChannel = 'restaurant_hub_promotions';
  static const String generalChannel = 'restaurant_hub_general';
  
  // Getters
  bool get isInitialized => _isInitialized.value;
  bool get permissionGranted => _permissionGranted.value;
  String get fcmToken => _fcmToken.value;
  List<Map<String, dynamic>> get notificationHistory => _notificationHistory;
  
  // Settings getters
  bool get orderUpdatesEnabled => _orderUpdatesEnabled.value;
  bool get promotionsEnabled => _promotionsEnabled.value;
  bool get newRestaurantsEnabled => _newRestaurantsEnabled.value;
  bool get deliveryUpdatesEnabled => _deliveryUpdatesEnabled.value;
  bool get paymentNotificationsEnabled => _paymentNotificationsEnabled.value;
  bool get soundEnabled => _soundEnabled.value;
  bool get vibrationEnabled => _vibrationEnabled.value;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeNotifications();
  }

  void _initializeServices() {
    _errorService = Get.find<ErrorService>();
    _auditService = Get.find<AuditService>();
  }

  Future<void> _initializeNotifications() async {
    if (!AppConstants.enablePushNotifications) return;
    
    try {
      // Initialize Firebase if not already initialized
      if (!Firebase.apps.isNotEmpty) {
        await Firebase.initializeApp();
      }
      
      // Initialize Firebase Messaging
      _firebaseMessaging = FirebaseMessaging.instance;
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Request permissions
      await _requestPermissions();
      
      // Get FCM token
      await _getFCMToken();
      
      // Set up message handlers
      _setupMessageHandlers();
      
      // Load notification settings
      await _loadNotificationSettings();
      
      _isInitialized.value = true;
      
      await _auditService.logAction(
        action: AuditAction.systemEvent,
        entityType: 'NotificationService',
        reason: 'Notification service initialized successfully',
        severity: AuditSeverity.info,
      );
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'NotificationService.initialize');
    }
  }

  Future<void> _initializeLocalNotifications() async {
    // Android initialization settings
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // iOS initialization settings
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    // Combined initialization settings
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    // Initialize with callback for notification taps
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    
    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  Future<void> _createNotificationChannels() async {
    final androidPlugin = _localNotifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      // Order updates channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          orderChannel,
          'Order Updates',
          description: 'Notifications about order status changes',
          importance: Importance.high,
          playSound: true,
          enableVibration: true,
        ),
      );
      
      // Promotions channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          promotionChannel,
          'Promotions',
          description: 'Special offers and promotions',
          importance: Importance.defaultImportance,
          playSound: true,
        ),
      );
      
      // General channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          generalChannel,
          'General',
          description: 'General app notifications',
          importance: Importance.defaultImportance,
        ),
      );
    }
  }

  Future<void> _requestPermissions() async {
    if (_firebaseMessaging == null) return;
    
    final settings = await _firebaseMessaging!.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );
    
    _permissionGranted.value = settings.authorizationStatus == AuthorizationStatus.authorized;
    
    if (!_permissionGranted.value) {
      _errorService.handleAppError(
        'Notification permissions not granted',
        context: 'NotificationService.requestPermissions',
      );
    }
  }

  Future<void> _getFCMToken() async {
    if (_firebaseMessaging == null || !_permissionGranted.value) return;
    
    try {
      final token = await _firebaseMessaging!.getToken();
      if (token != null) {
        _fcmToken.value = token;
        
        // In production, send this token to your server
        if (kDebugMode) {
          print('FCM Token: $token');
        }
      }
    } catch (e) {
      _errorService.handleAppError(e, context: 'NotificationService.getFCMToken');
    }
  }

  void _setupMessageHandlers() {
    if (_firebaseMessaging == null) return;
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Handle notification tap when app is terminated
    _firebaseMessaging!.getInitialMessage().then((message) {
      if (message != null) {
        _handleNotificationTap(message);
      }
    });
    
    // Listen for token refresh
    _firebaseMessaging!.onTokenRefresh.listen((token) {
      _fcmToken.value = token;
      // In production, update token on server
    });
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    // Add to history
    _addToHistory(message);
    
    // Show local notification if app is in foreground
    await _showLocalNotification(
      title: message.notification?.title ?? 'Restaurant Hub',
      body: message.notification?.body ?? '',
      payload: jsonEncode(message.data),
      channelId: _getChannelId(message.data['type']),
    );
  }

  Future<void> _handleNotificationTap(RemoteMessage message) async {
    // Add to history
    _addToHistory(message);
    
    // Navigate based on notification type
    final type = message.data['type'];
    final data = message.data;
    
    switch (type) {
      case 'order_update':
        final orderId = data['orderId'];
        if (orderId != null) {
          Get.toNamed(AppRoutes.orderTracking, arguments: {'orderId': orderId});
        }
        break;
      case 'promotion':
        Get.toNamed(AppRoutes.home);
        break;
      case 'new_restaurant':
        final restaurantId = data['restaurantId'];
        if (restaurantId != null) {
          Get.toNamed(AppRoutes.restaurantDetail, arguments: {'restaurantId': restaurantId});
        }
        break;
      default:
        Get.toNamed(AppRoutes.home);
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        final type = data['type'];
        
        switch (type) {
          case 'order_update':
            final orderId = data['orderId'];
            if (orderId != null) {
              Get.toNamed(AppRoutes.orderTracking, arguments: {'orderId': orderId});
            }
            break;
          default:
            Get.toNamed(AppRoutes.home);
        }
      } catch (e) {
        _errorService.handleAppError(e, context: 'NotificationService.onNotificationTapped');
      }
    }
  }

  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = generalChannel,
  }) async {
    if (!_permissionGranted.value) return;
    
    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: Importance.high,
      priority: Priority.high,
      playSound: _soundEnabled.value,
      enableVibration: _vibrationEnabled.value,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      details,
      payload: payload,
    );
  }

  void _addToHistory(RemoteMessage message) {
    final notification = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'title': message.notification?.title ?? '',
      'body': message.notification?.body ?? '',
      'data': message.data,
      'timestamp': DateTime.now().toIso8601String(),
      'read': false,
    };
    
    _notificationHistory.insert(0, notification);
    
    // Keep only recent notifications
    if (_notificationHistory.length > 100) {
      _notificationHistory.removeRange(100, _notificationHistory.length);
    }
  }

  String _getChannelId(String? type) {
    switch (type) {
      case 'order_update':
      case 'delivery_update':
        return orderChannel;
      case 'promotion':
        return promotionChannel;
      default:
        return generalChannel;
    }
  }

  String _getChannelName(String channelId) {
    switch (channelId) {
      case orderChannel:
        return 'Order Updates';
      case promotionChannel:
        return 'Promotions';
      default:
        return 'General';
    }
  }

  String _getChannelDescription(String channelId) {
    switch (channelId) {
      case orderChannel:
        return 'Notifications about order status changes';
      case promotionChannel:
        return 'Special offers and promotions';
      default:
        return 'General app notifications';
    }
  }

  Future<void> _loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _orderUpdatesEnabled.value = prefs.getBool('notification_order_updates') ?? true;
      _promotionsEnabled.value = prefs.getBool('notification_promotions') ?? true;
      _newRestaurantsEnabled.value = prefs.getBool('notification_new_restaurants') ?? true;
      _deliveryUpdatesEnabled.value = prefs.getBool('notification_delivery_updates') ?? true;
      _paymentNotificationsEnabled.value = prefs.getBool('notification_payment') ?? true;
      _soundEnabled.value = prefs.getBool('notification_sound') ?? true;
      _vibrationEnabled.value = prefs.getBool('notification_vibration') ?? true;
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'NotificationService.loadSettings');
    }
  }

  // Public methods for sending notifications
  Future<void> sendOrderUpdateNotification(OrderModel order) async {
    if (!_orderUpdatesEnabled.value) return;
    
    final title = 'Order Update';
    final body = 'Your order #${order.id.substring(0, 8)} is now ${order.status.displayName}';
    
    await _showLocalNotification(
      title: title,
      body: body,
      payload: jsonEncode({
        'type': 'order_update',
        'orderId': order.id,
      }),
      channelId: orderChannel,
    );
  }

  Future<void> sendPromotionNotification(String title, String message, {String? restaurantId}) async {
    if (!_promotionsEnabled.value) return;
    
    await _showLocalNotification(
      title: title,
      body: message,
      payload: jsonEncode({
        'type': 'promotion',
        'restaurantId': restaurantId,
      }),
      channelId: promotionChannel,
    );
  }

  Future<void> sendDeliveryUpdateNotification(String orderId, String message) async {
    if (!_deliveryUpdatesEnabled.value) return;
    
    await _showLocalNotification(
      title: 'Delivery Update',
      body: message,
      payload: jsonEncode({
        'type': 'delivery_update',
        'orderId': orderId,
      }),
      channelId: orderChannel,
    );
  }

  // Settings management
  Future<void> updateNotificationSettings({
    bool? orderUpdates,
    bool? promotions,
    bool? newRestaurants,
    bool? deliveryUpdates,
    bool? paymentNotifications,
    bool? sound,
    bool? vibration,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (orderUpdates != null) {
        _orderUpdatesEnabled.value = orderUpdates;
        await prefs.setBool('notification_order_updates', orderUpdates);
      }
      
      if (promotions != null) {
        _promotionsEnabled.value = promotions;
        await prefs.setBool('notification_promotions', promotions);
      }
      
      if (newRestaurants != null) {
        _newRestaurantsEnabled.value = newRestaurants;
        await prefs.setBool('notification_new_restaurants', newRestaurants);
      }
      
      if (deliveryUpdates != null) {
        _deliveryUpdatesEnabled.value = deliveryUpdates;
        await prefs.setBool('notification_delivery_updates', deliveryUpdates);
      }
      
      if (paymentNotifications != null) {
        _paymentNotificationsEnabled.value = paymentNotifications;
        await prefs.setBool('notification_payment', paymentNotifications);
      }
      
      if (sound != null) {
        _soundEnabled.value = sound;
        await prefs.setBool('notification_sound', sound);
      }
      
      if (vibration != null) {
        _vibrationEnabled.value = vibration;
        await prefs.setBool('notification_vibration', vibration);
      }
      
    } catch (e) {
      _errorService.handleAppError(e, context: 'NotificationService.updateSettings');
    }
  }

  // Mark notification as read
  void markAsRead(String notificationId) {
    final index = _notificationHistory.indexWhere((n) => n['id'] == notificationId);
    if (index != -1) {
      _notificationHistory[index]['read'] = true;
      _notificationHistory.refresh();
    }
  }

  // Clear all notifications
  void clearAllNotifications() {
    _notificationHistory.clear();
  }

  // Get unread count
  int get unreadCount => _notificationHistory.where((n) => !n['read']).length;
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  // Handle background messages
  // This runs in a separate isolate, so we can't access the service instance
  if (kDebugMode) {
    print('Background message: ${message.messageId}');
  }
}
