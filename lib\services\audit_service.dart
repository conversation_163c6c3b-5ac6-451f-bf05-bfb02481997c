import 'package:get/get.dart';
import '../models/audit_log.dart';
import '../controllers/user_controller.dart';

/// Service for managing audit logs and tracking admin actions
class AuditService extends GetxService {
  static AuditService get instance => Get.find<AuditService>();

  final RxList<AuditLog> _auditLogs = <AuditLog>[].obs;
  final RxBool _isLoading = false.obs;

  // Dependencies
  final UserController _userController = Get.find<UserController>();

  // Getters
  List<AuditLog> get auditLogs => List.unmodifiable(_auditLogs);
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _loadAuditLogs();
  }

  /// Load audit logs from storage
  Future<void> _loadAuditLogs() async {
    try {
      _isLoading.value = true;

      // In a real app, this would load from a database
      // For now, we'll create some demo audit logs
      await _createDemoAuditLogs();
    } catch (e) {
      print('Error loading audit logs: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create demo audit logs for testing
  Future<void> _createDemoAuditLogs() async {
    final now = DateTime.now();
    final demoLogs = [
      AuditLog(
        id: 'audit_001',
        userId: 'admin_001',
        userName: 'Admin User',
        action: AuditAction.create,
        entityId: 'user_123',
        entityType: 'User',
        reason: 'Created new customer <NAME_EMAIL>',
        ipAddress: '*************',
        userAgent: 'RestaurantHub Mobile App v1.0',
        timestamp: now.subtract(const Duration(hours: 2)),
        severity: AuditSeverity.info,
      ),
      AuditLog(
        id: 'audit_002',
        userId: 'admin_001',
        userName: 'Admin User',
        action: AuditAction.approve,
        entityId: 'restaurant_456',
        entityType: 'Restaurant',
        reason: 'Approved restaurant registration for Pizza Palace',
        ipAddress: '*************',
        userAgent: 'RestaurantHub Web Dashboard v1.0',
        timestamp: now.subtract(const Duration(hours: 4)),
        severity: AuditSeverity.info,
      ),
      AuditLog(
        id: 'audit_003',
        userId: 'admin_001',
        userName: 'Admin User',
        action: AuditAction.refund,
        entityId: 'order_789',
        entityType: 'Order',
        reason: 'Processed refund for order #789 - Customer complaint about food quality',
        ipAddress: '*************',
        userAgent: 'RestaurantHub Web Dashboard v1.0',
        timestamp: now.subtract(const Duration(hours: 6)),
        severity: AuditSeverity.warning,
      ),
      AuditLog(
        id: 'audit_004',
        userId: 'admin_001',
        userName: 'Admin User',
        action: AuditAction.systemConfig,
        entityId: 'settings_001',
        entityType: 'SystemSettings',
        reason: 'Updated delivery fee from \$2.99 to \$3.49',
        ipAddress: '*************',
        userAgent: 'RestaurantHub Web Dashboard v1.0',
        timestamp: now.subtract(const Duration(days: 1)),
        severity: AuditSeverity.info,
      ),
      AuditLog(
        id: 'audit_005',
        userId: 'admin_001',
        userName: 'Admin User',
        action: AuditAction.suspend,
        entityId: 'user_999',
        entityType: 'User',
        reason: 'Suspended user account due to multiple policy violations',
        ipAddress: '*************',
        userAgent: 'RestaurantHub Web Dashboard v1.0',
        timestamp: now.subtract(const Duration(days: 2)),
        severity: AuditSeverity.error,
      ),
    ];

    _auditLogs.addAll(demoLogs);
  }

  /// Log an admin action
  Future<void> logAction({
    required AuditAction action,
    required String entityId,
    required String entityType,
    String? reason,
    AuditSeverity severity = AuditSeverity.info,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
  }) async {
    final currentUser = _userController.currentUser;
    if (currentUser == null) return;

    final auditLog = AuditLog(
      id: 'audit_${DateTime.now().millisecondsSinceEpoch}',
      userId: currentUser.id,
      userName: currentUser.fullName,
      action: action,
      entityId: entityId,
      entityType: entityType,
      reason: reason,
      ipAddress: '*************', // In real app, get actual IP
      userAgent: 'RestaurantHub Mobile App v1.0', // In real app, get actual user agent
      timestamp: DateTime.now(),
      severity: severity,
      oldValues: oldValues,
      newValues: newValues,
    );

    // Add to local list
    _auditLogs.insert(0, auditLog);

    // In a real app, save to database/API
    await _saveAuditLog(auditLog);
  }

  /// Save audit log to persistent storage
  Future<void> _saveAuditLog(AuditLog auditLog) async {
    try {
      // In a real app, this would save to a database or API
      // For now, we'll just simulate the save
      await Future.delayed(const Duration(milliseconds: 100));

      print('Audit log saved: ${auditLog.action.displayName} - ${auditLog.reason ?? 'No reason provided'}');
    } catch (e) {
      print('Error saving audit log: $e');
    }
  }

  /// Get audit logs by action type
  List<AuditLog> getLogsByAction(AuditAction action) {
    return _auditLogs.where((log) => log.action == action).toList();
  }

  /// Get audit logs by severity
  List<AuditLog> getLogsBySeverity(AuditSeverity severity) {
    return _auditLogs.where((log) => log.severity == severity).toList();
  }

  /// Get audit logs by user
  List<AuditLog> getLogsByUser(String userId) {
    return _auditLogs.where((log) => log.userId == userId).toList();
  }

  /// Get audit logs by target
  List<AuditLog> getLogsByTarget(String entityId, String entityType) {
    return _auditLogs.where((log) => log.entityId == entityId && log.entityType == entityType).toList();
  }

  /// Get audit logs within date range
  List<AuditLog> getLogsByDateRange(DateTime startDate, DateTime endDate) {
    return _auditLogs.where((log) => log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate)).toList();
  }

  /// Search audit logs
  List<AuditLog> searchLogs(String query) {
    if (query.isEmpty) return _auditLogs;

    final lowercaseQuery = query.toLowerCase();
    return _auditLogs
        .where((log) =>
            (log.reason?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            log.userName.toLowerCase().contains(lowercaseQuery) ||
            log.entityType.toLowerCase().contains(lowercaseQuery) ||
            log.action.displayName.toLowerCase().contains(lowercaseQuery))
        .toList();
  }

  /// Get recent critical logs
  List<AuditLog> getCriticalLogs({int limit = 10}) {
    return _auditLogs
        .where((log) => log.severity == AuditSeverity.critical || log.severity == AuditSeverity.error)
        .take(limit)
        .toList();
  }

  /// Get logs summary for dashboard
  Map<String, int> getLogsSummary() {
    final summary = <String, int>{};

    for (final severity in AuditSeverity.values) {
      summary[severity.name] = _auditLogs.where((log) => log.severity == severity).length;
    }

    return summary;
  }

  /// Clear old audit logs (keep only recent ones)
  Future<void> clearOldLogs({int keepDays = 90}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: keepDays));
    _auditLogs.removeWhere((log) => log.timestamp.isBefore(cutoffDate));
  }

  /// Export audit logs (for compliance/reporting)
  Future<List<Map<String, dynamic>>> exportLogs({
    DateTime? startDate,
    DateTime? endDate,
    AuditSeverity? severity,
    AuditAction? action,
  }) async {
    var logs = _auditLogs.toList();

    // Apply filters
    if (startDate != null) {
      logs = logs.where((log) => log.timestamp.isAfter(startDate)).toList();
    }
    if (endDate != null) {
      logs = logs.where((log) => log.timestamp.isBefore(endDate)).toList();
    }
    if (severity != null) {
      logs = logs.where((log) => log.severity == severity).toList();
    }
    if (action != null) {
      logs = logs.where((log) => log.action == action).toList();
    }

    return logs.map((log) => log.toJson()).toList();
  }

  /// Refresh audit logs
  Future<void> refresh() async {
    await _loadAuditLogs();
  }
}
