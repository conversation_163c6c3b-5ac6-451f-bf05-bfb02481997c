import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../shared/custom_app_bar.dart';
import '../shared/loading_widget.dart';
import '../shared/empty_state_widget.dart';
import '../shared/snackbars.dart';

class AddressesScreen extends StatefulWidget {
  const AddressesScreen({super.key});

  @override
  State<AddressesScreen> createState() => _AddressesScreenState();
}

class _AddressesScreenState extends State<AddressesScreen> {
  final UserController _userController = Get.find<UserController>();
  final List<AddressModel> _addresses = [];

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() {
    final user = _userController.currentUser;
    if (user != null) {
      // For now, we'll use the user's current address as the default
      _addresses.clear();
      _addresses.add(user.stats.address);

      // Add some demo addresses
      _addresses.addAll([
        AddressModel(
          id: 'addr_002',
          type: 'Work',
          fullAddress: '456 Business Ave, Downtown',
          unit: 'Suite 200',
          isDefault: false,
          estimatedDelivery: '20-30 mins',
        ),
        AddressModel(
          id: 'addr_003',
          type: 'Other',
          fullAddress: '789 Friend Street, Suburb',
          unit: null,
          isDefault: false,
          estimatedDelivery: '25-35 mins',
        ),
      ]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Delivery Addresses',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddAddressDialog,
          ),
        ],
      ),
      body: Obx(() {
        final user = _userController.currentUser;
        if (user == null) {
          return const LoadingWidget(message: 'Loading addresses...');
        }

        if (_addresses.isEmpty) {
          return const EmptyStateWidget(
            title: 'No Addresses',
            message: 'Add your delivery addresses to get started.',
            icon: Icons.location_on_outlined,
            actionText: 'Add Address',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _addresses.length,
          itemBuilder: (context, index) {
            final address = _addresses[index];
            return _buildAddressCard(address);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAddressDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAddressCard(AddressModel address) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: address.isDefault ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    address.type,
                    style: TextStyle(
                      color: address.isDefault ? Theme.of(context).primaryColor : Colors.grey[600],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (address.isDefault) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Default',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAddressAction(value, address),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    if (!address.isDefault)
                      const PopupMenuItem(
                        value: 'default',
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 20),
                            SizedBox(width: 8),
                            Text('Set as Default'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              address.fullAddress,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (address.unit != null) ...[
              const SizedBox(height: 4),
              Text(
                address.unit!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  address.estimatedDelivery,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleAddressAction(String action, AddressModel address) {
    switch (action) {
      case 'edit':
        _showEditAddressDialog(address);
        break;
      case 'default':
        _setDefaultAddress(address);
        break;
      case 'delete':
        _showDeleteConfirmation(address);
        break;
    }
  }

  void _showAddAddressDialog() {
    _showAddressDialog();
  }

  void _showEditAddressDialog(AddressModel address) {
    _showAddressDialog(address: address);
  }

  void _showAddressDialog({AddressModel? address}) {
    final isEditing = address != null;
    final addressController = TextEditingController(text: address?.fullAddress ?? '');
    final unitController = TextEditingController(text: address?.unit ?? '');

    String selectedType = address?.type ?? 'Home';
    final types = ['Home', 'Work', 'Other'];

    Get.dialog(
      AlertDialog(
        title: Text(isEditing ? 'Edit Address' : 'Add Address'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'Address Type',
                  border: OutlineInputBorder(),
                ),
                items: types
                    .map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: addressController,
                decoration: const InputDecoration(
                  labelText: 'Full Address',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: unitController,
                decoration: const InputDecoration(
                  labelText: 'Unit/Apartment (Optional)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (addressController.text.trim().isEmpty) {
                ErrorSnackBar.show('Please enter a valid address');
                return;
              }

              final newAddress = AddressModel(
                id: isEditing ? address.id : 'addr_${DateTime.now().millisecondsSinceEpoch}',
                type: selectedType,
                fullAddress: addressController.text.trim(),
                unit: unitController.text.trim().isEmpty ? null : unitController.text.trim(),
                isDefault: address?.isDefault ?? false,
                estimatedDelivery: '15-25 mins', // Default delivery time
              );

              if (isEditing) {
                final index = _addresses.indexWhere((a) => a.id == address.id);
                if (index != -1) {
                  setState(() {
                    _addresses[index] = newAddress;
                  });
                }
              } else {
                setState(() {
                  _addresses.add(newAddress);
                });
              }

              Get.back();
              SuccessSnackBar.show(isEditing ? 'Address updated!' : 'Address added!');
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _setDefaultAddress(AddressModel address) {
    setState(() {
      for (int i = 0; i < _addresses.length; i++) {
        _addresses[i] = AddressModel(
          id: _addresses[i].id,
          type: _addresses[i].type,
          fullAddress: _addresses[i].fullAddress,
          unit: _addresses[i].unit,
          isDefault: _addresses[i].id == address.id,
          estimatedDelivery: _addresses[i].estimatedDelivery,
        );
      }
    });
    SuccessSnackBar.show('Default address updated!');
  }

  void _showDeleteConfirmation(AddressModel address) {
    if (address.isDefault) {
      ErrorSnackBar.show('Cannot delete default address');
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Delete Address'),
        content: Text('Are you sure you want to delete this address?\n\n${address.fullAddress}'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _addresses.removeWhere((a) => a.id == address.id);
              });
              Get.back();
              SuccessSnackBar.show('Address deleted!');
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
