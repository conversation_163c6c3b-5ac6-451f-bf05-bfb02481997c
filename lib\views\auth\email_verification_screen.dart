import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../shared/custom_input_field.dart';
import '../shared/custom_buttons.dart';
import '../shared/snackbars.dart';

class EmailVerificationScreen extends StatefulWidget {
  const EmailVerificationScreen({super.key});

  @override
  State<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  final _userController = Get.find<UserController>();

  String? email;

  @override
  void initState() {
    super.initState();
    // Get email from arguments or current user
    email = Get.arguments as String? ?? _userController.currentUser?.email;
    if (email == null) {
      Get.back(); // Return if no email provided
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  String? _validateCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Verification code is required';
    }
    if (value.length != 6) {
      return 'Please enter a valid 6-digit code';
    }
    return null;
  }

  Future<void> _verifyEmail() async {
    if (_formKey.currentState!.validate()) {
      final success = await _userController.verifyEmail(
        email!,
        _codeController.text.trim(),
      );

      if (success) {
        SuccessSnackBar.show('Email verified successfully!');
        Get.back(result: true);
      } else {
        ErrorSnackBar.show(_userController.errorMessage);
      }
    }
  }

  Future<void> _resendCode() async {
    final success = await _userController.sendEmailVerification(email!);

    if (success) {
      SuccessSnackBar.show('New verification code sent to your email');
    } else {
      ErrorSnackBar.show(_userController.errorMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (email == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Email'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Theme.of(context).primaryColor,
      ),
      body: Obx(() => _userController.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 20),

                      // Header
                      Icon(
                        Icons.mark_email_read,
                        size: 80,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(height: 24),

                      Text(
                        'Verify Your Email',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),

                      Text(
                        'We\'ve sent a 6-digit verification code to $email. Enter it below to verify your email address.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),

                      // Verification code field
                      CustomInputField(
                        controller: _codeController,
                        label: 'Verification Code',
                        hint: 'Enter 6-digit code',
                        prefixIcon: const Icon(Icons.security),
                        keyboardType: TextInputType.number,
                        validator: _validateCode,
                        maxLength: 6,
                      ),
                      const SizedBox(height: 32),

                      // Verify button
                      PrimaryButton(
                        text: 'Verify Email',
                        onPressed: _verifyEmail,
                      ),
                      const SizedBox(height: 16),

                      // Resend code button
                      TextButton(
                        onPressed: _resendCode,
                        child: const Text('Didn\'t receive the code? Resend'),
                      ),
                      const SizedBox(height: 24),

                      // Back button
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('Back'),
                      ),

                      const SizedBox(height: 20),

                      // Security note
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'For security reasons, the verification code will expire in 1 hour.',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )),
    );
  }
}
